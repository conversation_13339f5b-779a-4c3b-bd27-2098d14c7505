import { db } from '~/storages/indexdb';
import { ApiKey, Conversation } from '../types';
import { createApiClient } from './api/client';
import { MiniAppLocal as MiniApp } from '~/types/miniapp';
import { extractMessageText } from '@the-agent/shared';

export const getUserInfo = async (apiKey: ApiKey) => {
  const client = await createApiClient(apiKey.key);
  const user = await client.getUser();
  const now = new Date().toISOString();
  return {
    id: user.id,
    email: user.email,
    api_key_enabled: apiKey.enabled,
    api_key: apiKey.key,
    credits: user.balance.toString(),
    created_at: now,
    updated_at: now,
    selectedModelId: 'system',
    permission: user.permission,
  };
};

/**
 * sync all conversations and miniapps
 */
export const syncUserData = async ({
  userId,
  apiKey,
}: {
  userId: string;
  apiKey?: string;
}): Promise<Conversation[]> => {
  try {
    const client = await createApiClient(apiKey);

    await db.clearUserData(userId);

    const response = await client.syncUserData({ startFrom: 0 });

    const conversations = response.conversations.map(conv => {
      const convId = Number(conv.id);
      let title = 'New Chat';
      if (conv.messages?.[0]) {
        title = extractMessageText(conv.messages[0]);
      }

      return {
        id: convId,
        title: title.slice(0, 20) || 'New Chat',
        user_id: userId,
        messages: conv.messages,
        last_selected_at: conv.last_selected_at,
        type: conv.type ?? 'default',
        status: 'remote',
      } as Conversation;
    });
    await db.backfill(conversations);
    if (Array.isArray(response.miniapps)) {
      for (const app of response.miniapps) {
        const appLocal: MiniApp = { ...app, sync_status: 'remote' } as MiniApp;

        if (app.conversation) {
          let title = 'App Chat';
          if (app.conversation.messages?.[0]) {
            title = extractMessageText(app.conversation.messages[0]);
          }
          const appConversation = {
            id: Number(app.conversation.id),
            title: title.slice(0, 20) || app.name || 'App Chat',
            user_id: userId,
            messages: app.conversation.messages || [],
            last_selected_at: app.conversation.last_selected_at || Date.now(),
            type: app.conversation.type || 'miniapp',
            status: 'remote',
          } as Conversation;
          await db.backfill([appConversation]);
        }

        await db.saveApplication(appLocal);
      }
    }
    return conversations;
  } catch (error) {
    console.error('Error in syncUserData:', error);
    throw error;
  }
};
