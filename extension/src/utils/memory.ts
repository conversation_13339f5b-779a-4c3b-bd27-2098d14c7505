import { APIClient, MemoryMetadata, Message } from '@the-agent/shared';

/**
 * Filter tool messages to reduce data size
 */
export function filterToolMessage(msg: Message): Message | null {
  const toolName = msg.name || '';

  // Tools that produce large data - simplify their content
  const largeDataTools = [
    'WebToolkit_analyzePageDOM',
    'WebToolkit_extractText',
    'WebToolkit_screenshot',
    'DevToolkit_render',
  ];

  const isLargeDataTool = largeDataTools.some(tool => toolName.includes(tool));

  if (isLargeDataTool) {
    const originalResult = (msg.metadata?.tool_call_result as Record<string, any>) || {};

    const simplifiedToolCallResult = {
      ...originalResult,
      success: originalResult.success || true,
      data: 'Large data omitted for size reduction',
    };

    return {
      ...msg,
      content: JSON.stringify(simplifiedToolCallResult),
      metadata: {
        ...msg.metadata,
        tool_call_result: simplifiedToolCallResult,
      },
    };
  }

  // For other tools, keep the original message
  return msg;
}

export async function storeMemory(
  apiClient: APIClient,
  messages: Message[],
  metadata: MemoryMetadata
) {
  if (!apiClient || messages.length === 0) {
    return;
  }

  try {
    const semanticMessages: Message[] = [];
    const proceduralMessages: Message[] = [];

    messages.forEach(msg => {
      if (msg.role === 'user' || msg.role === 'assistant') {
        semanticMessages.push(msg);
      }

      if (msg.role === 'tool') {
        const filteredMsg = filterToolMessage(msg);
        proceduralMessages.push(filteredMsg || msg);
      }
    });

    if (semanticMessages.length > 0) {
      await apiClient.addMemory({
        messages: semanticMessages,
        config: {
          filters: {
            conversationId: metadata.conversationId?.toString(),
          },
          metadata: {
            ...metadata,
            memoryType: 'semantic',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        },
      });
    }

    if (proceduralMessages.length > 0) {
      await apiClient.addMemory({
        messages: proceduralMessages,
        config: {
          filters: {
            conversationId: metadata.conversationId?.toString(),
            agentId: metadata.agentId,
            taskId: metadata.taskId,
            workflowId: metadata.workflowId,
          },
          metadata: {
            ...metadata,
            memoryType: 'procedural',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        },
      });
    }
  } catch (error) {
    console.error('Failed to store memory:', error);
  }
}
