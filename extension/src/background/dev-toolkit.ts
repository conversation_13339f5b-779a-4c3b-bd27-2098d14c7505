import { WebInteractionResult, WebToolKitArguments } from '~/types/tools';
import { TabToolkit } from './tab-toolkit';

export class DevToolkit {
  async run(toolName: string, args: WebToolKitArguments): Promise<WebInteractionResult<unknown>> {
    const tab = await TabToolkit.getCurrentActiveTab();
    if (!tab?.id) {
      return { success: false, error: 'No active tab found' };
    }

    switch (toolName) {
      case 'getSimplifiedPageDOM': {
        return await this.getSimplifiedPageDOM(tab.id);
      }
      case 'getDetailedHtmlElement': {
        const { selector } = args as { selector: string };
        return await this.getDetailedHtmlElement(tab.id, selector);
      }
      case 'render': {
        return {
          success: true,
        };
      }
      default: {
        throw new Error(`Unknown DevToolkit operation: ${toolName}`);
      }
    }
  }

  async getSimplifiedPageDOM(tabId: number): Promise<WebInteractionResult<string>> {
    return await new Promise<WebInteractionResult<string>>((resolve, reject) => {
      chrome.tabs.sendMessage(
        tabId,
        {
          name: 'get-simplified-page-dom',
        },
        response => {
          if (chrome.runtime.lastError) {
            console.error('getSimplifiedPageDOM error:', chrome.runtime.lastError);
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          resolve(response);
        }
      );
    });
  }

  async getDetailedHtmlElement(
    tabId: number,
    selector: string
  ): Promise<WebInteractionResult<string>> {
    return await new Promise<WebInteractionResult<string>>((resolve, reject) => {
      chrome.tabs.sendMessage(
        tabId,
        {
          name: 'get-detailed-html-element',
          selector,
        },
        response => {
          if (chrome.runtime.lastError) {
            console.error('getDetailedHtmlElement error:', chrome.runtime.lastError);
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          resolve(response);
        }
      );
    });
  }
}
