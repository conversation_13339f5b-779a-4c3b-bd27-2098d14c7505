import { MemoryItem, MemoryRelation, RuntimeInput, TaskNode } from '@the-agent/shared';
import { ContextChunk } from '~/types';

/**
 * Builds a context chunk from a list of memories.
 * @param memories - semantic memories, procedural memories
 * @returns A context chunk containing the memories.
 */
export function buildMemoryContextChunk(memories: MemoryItem[]): ContextChunk | undefined {
  if (!memories || memories.length === 0) {
    return undefined;
  }

  const memoryContext = memories.map(mem => mem.memory).join('\n\n');

  return {
    title: 'Related memories from previous conversations',
    content: memoryContext,
  };
}

export function buildSiteMemoryContextPrompt(
  relatedSiteMessages: MemoryItem[] = []
): ContextChunk | undefined {
  if (relatedSiteMessages.length === 0) {
    return undefined;
  }
  const siteKnowledge = relatedSiteMessages.map(msg => msg.memory).join('\n\n');
  return {
    title: 'Site-Specific Memory',
    content: siteKnowledge,
  };
}

export function buildProceduralMemoryContextChunk(
  memories: MemoryItem[]
): ContextChunk | undefined {
  if (!memories || memories.length === 0) {
    return undefined;
  }

  // filter out procedural memories
  const proceduralMemories = memories.filter(mem => mem.metadata?.memoryType === 'procedural');

  if (proceduralMemories.length === 0) {
    return undefined;
  }

  const proceduralContext = proceduralMemories.map(mem => mem.memory).join('\n\n');
  return {
    title: 'Task Execution History',
    content: proceduralContext,
  };
}

export function buildGraphContextPrompt(
  graphRelations: MemoryRelation[] = []
): ContextChunk | undefined {
  if (graphRelations.length === 0) {
    return undefined;
  }
  let contextPrompt = '';
  for (const rel of graphRelations) {
    const src = rel.source;
    const dst = rel.destination;
    const relType = rel.relationship;
    contextPrompt += `- ${src} --[${relType}]--> ${dst}\n`;
  }
  return {
    title: 'Knowledge Graph Relations',
    content: contextPrompt,
  };
}

export function buildTaskContextPrompt(task: TaskNode): ContextChunk {
  let ctx = formatTask(task);
  if (task.runtimeInput) {
    ctx += `
### Input of the task

${task.runtimeInput}
`;
  } else {
    const inputs = task.getTaskInputs();
    if (inputs.length > 0) {
      ctx += formatTaskInputs(inputs);
    }
  }
  return {
    title: 'Task Details',
    content: ctx,
  };
}

function formatTask(task: TaskNode) {
  const taskDef = task.task;
  let taskInfo = `
- Id: ${task.id}
- Goal: ${taskDef.goal}
`;
  if (taskDef.repeat) {
    const repeat = taskDef.repeat as number;
    taskInfo += `- Repeat: ${repeat} times\n`;
  }
  if (task.parent) {
    taskInfo += `
### Parent Task
- Id: ${task.parent.id}
- Goal: ${task.parent.task.goal}
`;
  }
  if (taskDef.foreach) {
    const foreach = taskDef.foreach;
    taskInfo += `
### Foreach Task
This is a foreach task, you need to iterate the input with following task:
- Nested Task Id: ${foreach.id}
- Nested Task Goal: ${foreach.goal}
`;
    if ('repeat' in foreach) {
      const repeat = foreach.repeat as number;
      taskInfo += `- Repeat: ${repeat} times\n`;
    }
  }
  return taskInfo;
}

function formatTaskInputs(inputs: RuntimeInput[]) {
  return `
\n### Inputs of the task
Following are outputs collected from dependent tasks, you can use them as input of the current task:
${inputs.map(input => formatRuntimeInput(input)).join('\n')}
`;
}

function formatRuntimeInput(input: RuntimeInput) {
  if (input.status === 'error') {
    return `Task "${input.id}": this task failed with error ${input.output ?? 'N/A'}`;
  } else {
    return `Task "${input.id}": ${input.output ?? 'no output'}`;
  }
}
