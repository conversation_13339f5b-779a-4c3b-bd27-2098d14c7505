import type { PlasmoCSConfig } from 'plasmo';
import MurmurHash3 from 'imurmurhash';

import { TemplateRegisterRequest } from '@the-agent/shared';
import { SimpleTurndownService } from './SimpleTurndownService';

import { getElementIndexMap, refreshElementIndexMap } from './element';
import {
  COMMON_TAGS,
  InteractionType,
  INTERACTIVE_ATTRIBUTES,
  INTERACTIVE_CLASS_PATTERNS,
  INTERACTIVE_DATA_ATTRIBUTES,
  INTERACTIVE_ELEMENTS,
  INTERACTIVE_ID_PATTERNS,
  INTERACTIVE_ROLES,
  INTERACTIVE_SELECTORS,
  POTENTIAL_INTERACTIVE_SELECTORS,
  SKIP_TAGS,
} from './constants';
import { DomChangeEvent, WebInteractionResult } from '~/types/tools';
import {
  buildCompactDiffSimple,
  getAssociatedLabels,
  getElementAttributes,
  getElementDepth,
  getElementValue,
  getFormContext,
  getUniqueSelector,
  generateStableSelectors,
  isElementChecked,
  isElementHidden,
  type HighlightedElement,
} from './element';

export const config: PlasmoCSConfig = {
  matches: ['<all_urls>'],
  run_at: 'document_idle',
};

export interface DomElementInfo {
  x: number;
  y: number;
  visible: boolean;
  text?: string;
  html: string;
}

export interface DomSelector {
  type: 'css';
  selector: string;
}

export interface SummarizeResult {
  content: string;
  url: string;
  title: string;
}

function extractTagVectorFromDOM(doc: Document): number[] {
  const counts = new Map<string, number>();

  for (const tag of COMMON_TAGS) {
    const count = doc.getElementsByTagName(tag).length;
    counts.set(tag, count);
  }

  // Normalize
  const max = Math.max(...counts.values());
  return COMMON_TAGS.map(tag => (counts.get(tag) || 0) / (max || 1));
}

function extractDomShingles(doc: Document): string[] {
  const shingles: string[] = [];
  const walker = doc.createTreeWalker(doc.body, NodeFilter.SHOW_ELEMENT, null);

  let node = walker.currentNode as Element;
  while (node) {
    const tag = node.tagName.toLowerCase();
    const parent = node.parentElement?.tagName.toLowerCase() || '';
    shingles.push(`${parent}>${tag}`);
    node = walker.nextNode() as Element;
  }
  return shingles;
}

function computeSimHash(shingles: string[], bits = 64): bigint {
  const vector = Array<number>(bits).fill(0);

  shingles.forEach(shingle => {
    const hash = new MurmurHash3(shingle).result();
    // mix lower bits into upper 64 if needed
    for (let i = 0; i < bits; i++) {
      vector[i] += (hash >>> i) & 1 ? 1 : -1;
    }
  });

  const fingerprint = vector.map(v => (v >= 0 ? '1' : '0')).join('');
  return BigInt('0b' + fingerprint);
}

export function buildDomTemplateFunction(): TemplateRegisterRequest {
  const domain = window.location.hostname;
  const path = window.location.pathname;
  const tag_vector = extractTagVectorFromDOM(document);
  const shingles = extractDomShingles(document);
  const simhash = computeSimHash(shingles);
  return {
    domain,
    path,
    tag_vector,
    simhash: Number(simhash),
  };
}

function getComputedStyle(element: Element): CSSStyleDeclaration | null {
  try {
    return window.getComputedStyle(element);
  } catch {
    return null;
  }
}

export function getInteractionType(element: Element): InteractionType | null {
  if (!element || element.nodeType !== Node.ELEMENT_NODE) {
    return null;
  }

  const tagName = element.tagName.toLowerCase();
  const style = getComputedStyle(element);

  if (INTERACTIVE_ELEMENTS[tagName]) {
    if ('disabled' in element && (element as HTMLInputElement).disabled) {
      return null;
    }
    if ('readOnly' in element && (element as HTMLInputElement).readOnly) {
      return null;
    }
    return INTERACTIVE_ELEMENTS[tagName];
  }

  // Check for interactive ARIA roles
  const role = element.getAttribute('role')?.toLowerCase();

  if (role && INTERACTIVE_ROLES[role]) {
    return INTERACTIVE_ROLES[role];
  }

  // Check for interactive cursor style
  if (style && style.cursor === 'pointer') {
    return 'click';
  }

  // Check for common event handlers
  for (const [attr, type] of INTERACTIVE_ATTRIBUTES) {
    if (element.hasAttribute(attr)) {
      return type;
    }
  }

  // Check for contenteditable
  if ((element as HTMLElement).isContentEditable) {
    return 'input';
  }

  // Check for tabindex
  const tabindex = element.getAttribute('tabindex');
  if (tabindex !== null && tabindex !== '-1') {
    return 'click';
  }

  // Check for interactive class patterns
  const className = element.className || '';
  for (const [pattern, type] of INTERACTIVE_CLASS_PATTERNS) {
    if (typeof className === 'string' && pattern.test(className)) {
      if (style) {
        if (
          style.cursor === 'pointer' ||
          style.userSelect === 'none' ||
          parseFloat(style.opacity) > 0.5
        ) {
          return type;
        }
      }

      const textContent = element.textContent?.trim();
      if (textContent || element.children.length > 0) {
        return type;
      }
    }
  }

  for (const [attr, type] of INTERACTIVE_DATA_ATTRIBUTES) {
    if (element.hasAttribute(attr)) {
      return type;
    }
  }

  // Check for specific patterns in id or data-testid
  const id = element.id || '';
  const testId = element.getAttribute('data-testid') || '';
  for (const [pattern, type] of INTERACTIVE_ID_PATTERNS) {
    if (id && pattern.test(id)) {
      return type;
    }
    if (testId && pattern.test(testId)) {
      return type;
    }
  }

  // Check for styled interactive elements
  if (style) {
    const hasBoxShadow = style.boxShadow && style.boxShadow !== 'none';
    const hasBorder = style.border && style.border !== 'none' && style.borderWidth !== '0px';
    const hasBackground =
      style.backgroundColor &&
      style.backgroundColor !== 'rgba(0, 0, 0, 0)' &&
      style.backgroundColor !== 'transparent';
    const hasTransition = style.transition && style.transition !== 'none';

    if ((hasBoxShadow || hasBorder || hasBackground) && hasTransition) {
      const textContent = element.textContent?.trim();
      if (textContent && textContent.length < 100) {
        return 'click';
      }
    }
  }
  return null;
}

export function findInteractiveElements(scopeElement?: Element): Map<Element, InteractionType> {
  const foundElements = new Map<Element, InteractionType>();
  const searchRoot = scopeElement || document;

  const selectors = Object.keys(INTERACTIVE_SELECTORS).join(',');
  const selectorElements = searchRoot.querySelectorAll(selectors);
  selectorElements.forEach(element => {
    const interactionType = getInteractionType(element);
    if (interactionType) {
      foundElements.set(element, interactionType);
    }
  });

  const potentialElements = searchRoot.querySelectorAll(POTENTIAL_INTERACTIVE_SELECTORS.join(','));
  potentialElements.forEach(element => {
    const interactionType = getInteractionType(element);
    const stype = getComputedStyle(element);
    if (stype && stype.cursor === 'pointer' && interactionType) {
      foundElements.set(element, interactionType);
    }
  });

  // Additional scan for elements with pointer cursor
  const allElements = searchRoot.querySelectorAll('*');
  allElements.forEach(element => {
    if (foundElements.has(element)) return;
    const interactionType = getInteractionType(element);
    if (interactionType) {
      foundElements.set(element, interactionType);
    }
  });
  return foundElements;
}

export function groupElementsBySemanticSegment(
  elements: HighlightedElement[]
): Record<string, HighlightedElement[]> {
  const grouped: Record<string, HighlightedElement[]> = {
    header: [],
    nav: [],
    main: [],
    section: [],
    article: [],
    aside: [],
    footer: [],
    other: [],
  };
  const semanticSelectors = ['header', 'nav', 'main', 'section', 'article', 'aside', 'footer'];
  const semanticElements = new Map<Element, string>();

  semanticSelectors.forEach(tagName => {
    const elements = document.querySelectorAll(tagName);
    elements.forEach(element => {
      semanticElements.set(element, tagName);
    });
  });

  elements.forEach(element => {
    let assigned = false;

    for (const [semanticElement, segmentType] of semanticElements.entries()) {
      if (element.element && semanticElement.contains(element.element)) {
        grouped[segmentType].push(element);
        assigned = true;
        break;
      }
    }
    if (!assigned) {
      grouped.other.push(element);
    }
  });

  return grouped;
}

export function analyzePageDOM(opts: { selector?: string; generateDiff?: boolean }): {
  elements: HighlightedElement[];
  diff?: DomChangeEvent | null;
} {
  if (!document || document.readyState === 'loading') {
    throw new Error('Document not ready for analysis');
  }
  const highlightedElements: HighlightedElement[] = [];
  // Find scope element if selector is provided
  let scopeElements: Element[] | undefined;
  if (opts.selector) {
    try {
      scopeElements = Array.from(document.querySelectorAll(opts.selector));
      if (!scopeElements || scopeElements.length === 0) {
        throw new Error(`Element with selector "${opts.selector}" not found`);
      }
    } catch (error) {
      throw new Error(`Invalid selector or element not found: ${opts.selector} ${error}`);
    }
  }

  const elements: Map<Element, InteractionType> = new Map();
  if (scopeElements && scopeElements.length > 0) {
    for (const scopeElement of scopeElements) {
      findInteractiveElements(scopeElement).forEach((type, e) => elements.set(e, type));
    }
  } else {
    findInteractiveElements().forEach((type, e) => elements.set(e, type));
  }

  elements.forEach((interactionType, element) => {
    if (highlightedElements.some(he => he.element === element)) {
      return;
    }

    // Skip unwanted elements before creating HighlightedElement
    if (SKIP_TAGS.has(element.tagName.toLowerCase())) {
      return;
    }

    const visible = !isElementHidden(element);
    if (!visible) {
      return;
    }

    const tagName = element.tagName.toLowerCase();
    const attributes = getElementAttributes(element);

    const associatedLabels = getAssociatedLabels(element);
    const { formId, formName } = getFormContext(element);

    highlightedElements.push({
      element: element,
      depth: getElementDepth(element),
      tagName,
      type: attributes.type || tagName,
      interactionType,
      attributes,
      isVisible: visible,
      textContent: element.textContent?.trim() || undefined,
      id: element.id || undefined,
      selector: getUniqueSelector(element),
      selectors: generateStableSelectors(element),
      value: getElementValue(element),
      placeholder: attributes.placeholder,
      ariaLabel: attributes['aria-label'],
      role: attributes.role,
      disabled: element.hasAttribute('disabled') || attributes['aria-disabled'] === 'true',
      required: element.hasAttribute('required') || attributes['aria-required'] === 'true',
      checked: isElementChecked(element),
      selected: element.hasAttribute('selected') || attributes['aria-selected'] === 'true',
      formId,
      formName,
      associatedLabels: associatedLabels.length > 0 ? associatedLabels : undefined,
    });
  });

  if (opts.generateDiff) {
    const before = Object.values(window.__elementIndexMap__ || {});
    refreshElementIndexMap(highlightedElements);
    const diff = buildCompactDiffSimple(before, highlightedElements);
    return { elements: highlightedElements, diff };
  } else {
    refreshElementIndexMap(highlightedElements);
    return { elements: highlightedElements };
  }
}

export function getSimplifiedPageDom(): string {
  try {
    // Create a temporary DOM element to parse the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = document.body.outerHTML;

    // Remove unwanted elements entirely
    const unwantedSelectors = [
      'script',
      'noscript',
      'style',
      'link[rel="stylesheet"]',
      'meta',
      'svg',
      'iframe',
      'embed',
      'object',
      'canvas',
      'audio',
      'video',
      'source',
      'track',
      'link',
    ];

    unwantedSelectors.forEach(selector => {
      const elements = tempDiv.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });

    // Clean attributes from all remaining elements
    const allElements = tempDiv.querySelectorAll('*');
    allElements.forEach(element => {
      // Get all attribute names
      const attributesToRemove: string[] = [];

      for (let i = 0; i < element.attributes.length; i++) {
        const attr = element.attributes[i];
        const attrName = attr.name.toLowerCase();

        // Remove specific attributes
        if (
          attrName === 'class' ||
          attrName === 'style' ||
          attrName.startsWith('data-') ||
          attrName.startsWith('on') || // event handlers
          attrName === 'tabindex' ||
          attrName === 'contenteditable' ||
          attrName.startsWith('aria-') ||
          attrName === 'cellpadding' ||
          attrName === 'cellspacing'
        ) {
          attributesToRemove.push(attrName);
        }
      }

      // Remove the identified attributes
      attributesToRemove.forEach(attrName => {
        element.removeAttribute(attrName);
      });
    });

    // Return the cleaned HTML
    return tempDiv.innerHTML;
  } catch (error) {
    console.error('Error filtering HTML:', error);
    // Fallback: return original HTML if parsing fails
    return document.body.outerHTML;
  }
}

export function getDetailedHtmlElement(selector: string): string {
  try {
    let element: Element | null = null;

    // Handle XPath selectors (start with '/')
    if (selector.startsWith('/')) {
      try {
        const result = document.evaluate(
          selector,
          document,
          null,
          XPathResult.FIRST_ORDERED_NODE_TYPE,
          null
        );
        element = result.singleNodeValue as Element | null;
      } catch (error) {
        console.error('Invalid XPath selector:', selector, error);
        return '';
      }
    } else {
      // Handle CSS selectors
      try {
        element = document.querySelector(selector);
      } catch (error) {
        console.error('Invalid CSS selector:', selector, error);
        return '';
      }
    }

    if (!element) {
      console.warn('Element not found for selector:', selector);
      return '';
    }

    // Return the complete HTML of the element
    return element.outerHTML;
  } catch (error) {
    console.error('Error getting element HTML:', error);
    return '';
  }
}

export type TreeNode = HighlightedElement & { children?: TreeNode[] };

export function buildElementTree(elements: HighlightedElement[]): TreeNode[] {
  const elMap = new Map<Element, TreeNode>();
  const roots: TreeNode[] = [];

  // Wrap each HighlightedElement as a TreeNode and index by DOM element
  for (const el of elements) {
    elMap.set(el.element, { ...el, children: [] });
  }

  for (const node of elMap.values()) {
    let current = node.element.parentElement;
    let foundParent: TreeNode | undefined = undefined;

    while (current) {
      const potentialParent = elMap.get(current);
      if (potentialParent) {
        foundParent = potentialParent;
        break;
      }
      current = current.parentElement;
    }

    if (foundParent) {
      foundParent.children!.push(node);
    } else {
      roots.push(node);
    }
  }

  return roots;
}

// Example output:
// - [1] form [action="/login", method="post"]
//   - [2] input [label="Username (Enter Username)", type="text", required]
//   - [3] button [label="Login", role="button", action="click"]

function isObviousInteractive(el: TreeNode): boolean {
  if (INTERACTIVE_ELEMENTS[el.tagName.toLowerCase()]) {
    return true;
  }
  if (el.role && INTERACTIVE_ROLES[el.role.toLowerCase()]) {
    return true;
  }
  return false;
}

function cleanUpLabel(label: string): string {
  const lines = label.split('\n');
  const cleanedLines = lines.map(line => line.trim()).filter(line => line.length > 0);
  return cleanedLines.join(', ');
}

function formatElementLine(el: TreeNode, index: number): string | undefined {
  // Base line with tag name
  const line = `- [${index}] ${el.tagName.toLowerCase()}`;

  // Build attributes summary
  const attrList: string[] = [];

  if (!isObviousInteractive(el)) {
    attrList.push(`action="${el.interactionType}"`);
  }

  let hasSemanticAttributes = false;
  if (el.ariaLabel || el.placeholder || el.textContent) {
    const label = el.ariaLabel || el.placeholder || el.textContent?.slice(0, 50).trim();
    if (label) {
      const labelText = `label="${cleanUpLabel(label)}"`;
      if (el.associatedLabels?.length) {
        const associatedLabels = cleanUpLabel(el.associatedLabels?.join(', '));
        attrList.push(`${labelText} (${associatedLabels})"`);
      } else {
        attrList.push(labelText);
      }
      hasSemanticAttributes = true;
    }
  }

  if (el.value) {
    attrList.push(`value="${el.value.slice(0, 50).trim()}"`);
    hasSemanticAttributes = true;
  }
  if (el.formName) {
    attrList.push(`formName="${el.formName}"`);
    hasSemanticAttributes = true;
  }

  if (el.type !== el.tagName) attrList.push(`type="${el.type}"`);
  if (el.role) attrList.push(`role="${el.role}"`);
  if (el.disabled) attrList.push('disabled');
  if (el.checked) attrList.push('checked');

  // Add selectors if they exist and are not empty
  if (el.selectors && el.selectors.primary) {
    attrList.push(`selector="${el.selectors.primary}"`);
    hasSemanticAttributes = true;
  }

  if (hasSemanticAttributes) {
    return `${line} [${attrList.join(', ')}]`;
  }
}

function formatElementTree(tree: TreeNode[], depth: number = 0): string {
  const lines: string[] = [];

  for (const node of tree) {
    const indent = '  '.repeat(depth); // 2-space indent
    const line = formatElementLine(node, node.index!);
    if (line) {
      lines.push(`${indent}${line.trim()}`);
    }

    if (node.children && node.children.length > 0) {
      const childrenLines = formatElementTree(node.children, depth + 1).trimEnd();
      if (childrenLines.length > 0) {
        lines.push(childrenLines);
      }
    }
  }

  return lines.join('\n');
}

export function formatHighlightedElements(elements: HighlightedElement[]): string {
  if (elements.length === 0) {
    return 'No interactive elements found on this page.';
  }

  const tree = buildElementTree(elements);
  return formatElementTree(tree);
}

/**
 * Returns the markdown conversion function to be executed in the tab context
 */
export function getMarkdownConversionFunction(): SummarizeResult {
  try {
    const turndownService = new SimpleTurndownService();

    let html: string;
    try {
      html = document.documentElement.outerHTML;
    } catch {
      html = document.body?.outerHTML || '';
    }

    const markdown = turndownService.turndown(html);

    return {
      content: markdown.trim(),
      url: window.location.href,
      title: document.title,
    };
  } catch {
    // Fallback to plain text extraction
    const text =
      document.body?.innerText ||
      document.documentElement.innerText ||
      'Error extracting page text';

    return {
      content: text,
      url: window.location.href,
      title: document.title,
    };
  }
}

function resolveSelectorOrIndex(selectorOrIndex: string): {
  element?: HTMLElement;
  error?: string;
} {
  let element: HTMLElement | null = null;

  // Handle index-based selection
  if (/^\d+$/.test(selectorOrIndex)) {
    const index = parseInt(selectorOrIndex);
    const elementMap = getElementIndexMap();
    if (!elementMap || !elementMap[index]) {
      return { error: `No element found at index ${index}` };
    }
    const highlightedElement = elementMap[index];
    return { element: highlightedElement.element as HTMLElement };
  }

  const selector = selectorOrIndex;

  // Handle CSS selectors
  try {
    element = document.querySelector(selector);
  } catch (error) {
    return { error: `Invalid CSS selector: "${selector}". Error: ${error}` };
  }

  if (element) {
    return { element: element as HTMLElement };
  } else {
    return { error: `Element with selector "${selector}" not found.` };
  }
}

function scrollToElement(selectorOrIndex: string): WebInteractionResult<string> {
  const { element, error } = resolveSelectorOrIndex(selectorOrIndex);
  if (error) {
    return { success: false, error: error };
  }
  if (!element) {
    return { success: false, error: 'Element not found' };
  }

  // Scroll element into view
  element.scrollIntoView({ behavior: 'smooth', block: 'center' });

  return {
    success: true,
    data: `Success: Scrolled to element "${element.tagName.toLowerCase()}" with text "${element.textContent?.trim() || 'no text'}"`,
  };
}

function getElementInfo(selectorOrIndex: string): WebInteractionResult<{
  x: number;
  y: number;
  visible: boolean;
  selector: string;
  html: string;
  value: string;
}> {
  const { element, error } = resolveSelectorOrIndex(selectorOrIndex);
  if (error) {
    return { success: false, error: error };
  }
  if (!element) {
    return { success: false, error: 'Element not found' };
  }

  const rect = element.getBoundingClientRect();
  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;

  // Get element value if it's an input element
  let value = '';
  if (element.getAttribute('contenteditable') === 'true') {
    value = element.textContent || '';
  } else if (
    element.tagName.toLowerCase() === 'input' ||
    element.tagName.toLowerCase() === 'textarea'
  ) {
    value = (element as HTMLInputElement | HTMLTextAreaElement).value;
  }

  // Determine the selector to return
  let selector: string;
  if (/^\d+$/.test(selectorOrIndex)) {
    // If it's an index, calculate the unique selector
    selector = getUniqueSelector(element);
  } else {
    // If it's already a selector, return it directly
    selector = selectorOrIndex;
  }

  return {
    success: true,
    data: {
      x: centerX,
      y: centerY,
      visible: rect.width > 0 && rect.height > 0,
      selector: selector,
      html: element.outerHTML,
      value: value,
    },
  };
}

// Message listeners for content script communication
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  (async () => {
    try {
      // Security check: verify the message comes from our extension's background script
      if (!sender || sender.id !== chrome.runtime.id) {
        console.warn('Message received from unauthorized sender:', sender);
        sendResponse({
          success: false,
          error: 'Unauthorized sender',
        });
        return true;
      }

      switch (request.name) {
        case 'build-dom-template': {
          const result = buildDomTemplateFunction();
          sendResponse({ success: true, data: result });
          return true;
        }

        case 'build-dom-tree': {
          const { selector, generateDiff } = request;
          const results = analyzePageDOM({ selector, generateDiff });
          const formatted = formatHighlightedElements(results.elements);
          sendResponse({ success: true, data: formatted });
          return true;
        }

        case 'get-simplified-page-dom': {
          const simplifiedPageDom = getSimplifiedPageDom();
          sendResponse({ success: true, data: simplifiedPageDom });
          return true;
        }

        case 'get-detailed-html-element': {
          const result = getDetailedHtmlElement(request.selector);
          sendResponse(result);
          return true;
        }

        case 'refresh-dom-tree': {
          const results = analyzePageDOM({ generateDiff: request.generateDiff });
          if (request.generateDiff) {
            sendResponse({ success: true, data: results.diff });
          } else {
            sendResponse({ success: true, data: null });
          }
          return true;
        }

        case 'summarize-page': {
          const summaryResult = getMarkdownConversionFunction();
          sendResponse({ success: true, data: summaryResult });
          return true;
        }

        case 'get-element': {
          const result = getElementInfo(request.selectorOrIndex);
          sendResponse(result);
          return true;
        }

        case 'scroll': {
          const scrollResult = scrollToElement(request.selectorOrIndex);
          sendResponse(scrollResult);
          return true;
        }
        // Don't handle other message types - let other listeners handle them
      }
    } catch (error) {
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  })();
});
