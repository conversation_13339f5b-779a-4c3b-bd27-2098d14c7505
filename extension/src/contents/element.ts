import { CompactChangeItem, DomChangeEvent } from '~/types';
import { InteractionType } from './constants';

// Define all necessary types and interfaces within the function scope
export interface DOMElementNode {
  element: Element;
  tagName: string;
  attributes: Record<string, string>;
  textContent?: string;
  id?: string;
  selector: string;
  rect?: DOMRect;
  isVisible?: boolean;
  isInViewport?: boolean;
}

export interface HighlightedElement extends Omit<DOMElementNode, 'element'> {
  element: Element;
  index?: number;
  type: string;
  interactionType: InteractionType;
  depth: number;
  value?: string;
  placeholder?: string;
  ariaLabel?: string;
  role?: string;
  disabled?: boolean;
  required?: boolean;
  checked?: boolean;
  selected?: boolean;
  formId?: string;
  formName?: string;
  associatedLabels?: string[];
  // Enhanced selector options for better stability
  selectors?: {
    primary: string;
    alternatives: string[];
  };
}

export type DomChangeReport = {
  nav?: { urlChanged: boolean; titleChanged: boolean };
  summary: {
    added: number;
    removed: number;
    textChanged: number;
    attrChanged: number;
    visibleChanged: number;
  };
  changes: Array<{
    type: 'added' | 'removed' | 'text' | 'attr' | 'visible';
    selector: string; // stable-ish CSS path
    role?: string; // aria role if any
    bbox?: { x: number; y: number; w: number; h: number }; // viewport box (rounded)
    textBefore?: string; // short excerpt (e.g. 80 chars)
    textAfter?: string;
    attr?: { name: string; before?: string; after?: string };
    important?: boolean; // near the interacted element or in viewport
  }>;
};

// Use Symbol to avoid conflicts with page scripts
const ELEMENT_INDEX_MAP_KEY = Symbol('__elementIndexMap__');

// Safe way to access element index map
export function getElementIndexMap(): Record<number, HighlightedElement> | undefined {
  return (window as any)[ELEMENT_INDEX_MAP_KEY];
}

export function setElementIndexMap(map: Record<number, HighlightedElement>): void {
  (window as any)[ELEMENT_INDEX_MAP_KEY] = map;
}

export function getElementAttributes(element: Element): Record<string, string> {
  const attributes: Record<string, string> = {};
  for (let i = 0; i < element.attributes.length; i++) {
    const attr = element.attributes[i];
    attributes[attr.name] = attr.value;
  }
  return attributes;
}

export function getElementValue(element: Element): string | undefined {
  if ('value' in element) {
    return (element as HTMLInputElement).value || undefined;
  }
  return element.getAttribute('value') || undefined;
}

export function getFormContext(element: Element): { formId?: string; formName?: string } {
  let formId: string | undefined;
  let formName: string | undefined;

  if ('form' in element) {
    const form = (element as HTMLInputElement).form;
    if (form) {
      formId = form.id || undefined;
      formName = form.getAttribute('name') || undefined;
    }
  } else {
    let parent = element.parentElement;
    while (parent && parent !== document.body) {
      if (parent.tagName === 'FORM') {
        formId = parent.id || undefined;
        formName = parent.getAttribute('name') || undefined;
        break;
      }
      parent = parent.parentElement;
    }
  }

  return { formId, formName };
}

export function isElementChecked(element: Element): boolean {
  if ('checked' in element) {
    return (element as HTMLInputElement).checked;
  }
  return element.getAttribute('aria-checked') === 'true';
}

export function getAssociatedLabels(element: Element): string[] {
  const labels: string[] = [];

  if (['input', 'select', 'textarea'].includes(element.tagName.toLowerCase())) {
    if (element.id) {
      const labelElements = document.querySelectorAll(`label[for="${element.id}"]`);
      labelElements.forEach(label => {
        if (label.textContent) {
          labels.push(label.textContent.trim());
        }
      });
    }
    let parent = element.parentElement;
    while (parent && parent !== document.body) {
      if (parent.tagName === 'LABEL' && parent.textContent) {
        labels.push(parent.textContent.trim());
        break;
      }
      parent = parent.parentElement;
    }
  }
  return labels.map(label => label.trim()).filter(label => label.length > 0);
}

// Utility functions
export function isElementHidden(element: Element): boolean {
  const style = element.getAttribute('style');
  const computedStyle = getComputedStyle(element);

  return (
    element.hasAttribute('hidden') ||
    (element.hasAttribute('aria-hidden') && element.getAttribute('aria-hidden') === 'true') ||
    (style != null &&
      (style.includes('display: none') ||
        style.includes('display:none') ||
        style.includes('visibility: hidden') ||
        style.includes('visibility:hidden'))) ||
    (computedStyle != null &&
      (computedStyle.display === 'none' ||
        computedStyle.visibility === 'hidden' ||
        computedStyle.opacity === '0'))
  );
}

export function getElementDepth(el: Element): number {
  let depth = 0;
  let current = el.parentElement;

  while (current && current !== document.body && current !== document.documentElement) {
    depth++;
    current = current.parentElement;
  }

  return depth;
}

function getUniqueCssSelector(el: Element): string | null {
  if (!el || el.nodeType !== Node.ELEMENT_NODE) return '';

  const isUnique = (selector: string) => document.querySelectorAll(selector).length === 1;

  // Try ID (fastest and usually unique)
  if (el.id && isUnique(`#${CSS.escape(el.id)}`)) {
    return `#${CSS.escape(el.id)}`;
  }

  // Try data-testid
  const testId = el.getAttribute('data-testid');
  if (testId && isUnique(`[data-testid="${testId}"]`)) {
    return `[data-testid="${testId}"]`;
  }

  // Try name
  const name = el.getAttribute('name');
  if (name && isUnique(`[name="${name}"]`)) {
    return `[name="${name}"]`;
  }

  // Try unique class
  if (el.classList.length === 1) {
    const className = el.classList[0];
    if (isUnique(`.${CSS.escape(className)}`)) {
      return `.${CSS.escape(className)}`;
    }
  }

  return null;
}

/**
 * Stable attributes in priority order (used by generateStableSelectors)
 */
const STABLE_ATTRIBUTES = [
  'data-testid', // Priority 1: Most semantic
  'name', // Priority 2: Stable for forms
  'aria-label', // Priority 3: Semantic and accessible
  'role', // Priority 4: Semantic
  'title', // Priority 5: Stable but may be long
  'placeholder', // Priority 6: Good for inputs
  'type', // Priority 7: Good for form elements
] as const;

/**
 * Internal function to generate stable selectors
 */
function generateSelectorsInternal(element: Element): {
  primary: string;
  alternatives: string[];
} {
  const selectors: string[] = [];
  const alternatives: string[] = [];

  // Try CSS selector first
  const cssSelector = getUniqueCssSelector(element);
  if (cssSelector) {
    selectors.push(cssSelector);
  }

  // Generate attribute-based selectors
  for (const attr of STABLE_ATTRIBUTES) {
    const value = element.getAttribute(attr);
    if (value) {
      const selector = `[${attr}="${CSS.escape(value)}"]`;
      try {
        if (document.querySelectorAll(selector).length === 1) {
          selectors.push(selector);
        } else {
          alternatives.push(selector);
        }
      } catch {
        // Invalid selector, skip
      }
    }
  }

  // Add tag-based selector as alternative
  const tagSelector = element.tagName.toLowerCase();
  alternatives.push(tagSelector);

  // Remove duplicates and limit alternatives
  const uniqueAlternatives = [...new Set(alternatives)].slice(0, 5);

  return {
    primary: selectors[0] || '',
    alternatives: uniqueAlternatives,
  };
}

/**
 * Generate multiple stable selectors for an element, ordered by reliability
 */
export function generateStableSelectors(element: Element): {
  primary: string;
  alternatives: string[];
} {
  return generateSelectorsInternal(element);
}

export function getUniqueSelector(element: Element): string {
  const result = generateStableSelectors(element);
  return result.primary || '';
}

/**
 * Internal function to refresh the element index map with incremental updates.
 */
export function refreshElementIndexMap(elements: HighlightedElement[]): void {
  // If no existing map, rebuild from scratch
  const elementMap = getElementIndexMap();
  if (!elementMap) {
    let index = window.__nextElementIndex__ ?? 0;
    setElementIndexMap(
      elements.reduce(
        (acc, el) => {
          el.index = index;
          acc[index++] = el;
          return acc;
        },
        {} as Record<number, HighlightedElement>
      )
    );
    window.__nextElementIndex__ = index;
    return;
  }

  // Create a set of current elements for efficient lookup (using direct element references)
  const currentElements = new Set(elements.map(el => el.element));

  // Get existing map entries
  const existingMap = getElementIndexMap();
  if (!existingMap) {
    return;
  }
  const existingIndices = Object.keys(existingMap).map(Number);

  // Step 1: Remove elements from map if they're not in current elements list
  for (const index of existingIndices) {
    const existingHighlightedElement = existingMap[index];
    if (!currentElements.has(existingHighlightedElement.element)) {
      delete existingMap[index];
    }
  }

  // Step 2: Add new elements to map if they're in elements list but not in map
  const existingElements = new Set(Object.values(existingMap).map(el => el.element));
  let nextIndex = window.__nextElementIndex__ ?? 0;
  for (const highlightedElement of elements) {
    // If element is not already in the map, add it with a new index
    if (!existingElements.has(highlightedElement.element)) {
      highlightedElement.index = nextIndex;
      existingMap[nextIndex++] = highlightedElement;
    } else {
      // Element exists in map, make sure the current element has the correct index
      const existingEntry = Object.values(existingMap).find(
        el => el.element === highlightedElement.element
      );
      if (existingEntry) {
        highlightedElement.index = existingEntry.index;
      }
    }
  }

  window.__nextElementIndex__ = nextIndex;
  setElementIndexMap(existingMap);
}

/** Smallest-possible compact diff, viewport-only, top-K per type. */
export function buildCompactDiffSimple(
  before: HighlightedElement[],
  after: HighlightedElement[],
  opts: { perTypeLimit?: number; textClip?: number; attrs?: string[]; vpMargin?: number } = {}
): DomChangeEvent | null {
  const K = opts.perTypeLimit ?? 3;
  const CLIP = opts.textClip ?? 80;
  const VP_PAD = opts.vpMargin ?? 4;
  const MEANINGFUL = new Set(
    opts.attrs ?? ['class', 'href', 'src', 'value', 'role', 'title', 'aria-label']
  );

  const clip = (s: string) => (s.length > CLIP ? s.slice(0, CLIP) + '...' : s);
  const norm = (s?: string | null) => (s ?? '').replace(/\s+/g, ' ').trim();

  const inViewport = (r?: DOMRect) => {
    if (!r) return false;
    const vx = -VP_PAD,
      vy = -VP_PAD,
      vw = window.innerWidth + VP_PAD * 2,
      vh = window.innerHeight + VP_PAD * 2;
    return (
      r.width > 0 &&
      r.height > 0 &&
      r.x < vx + vw &&
      r.x + r.width > vx &&
      r.y < vy + vh &&
      r.y + r.height > vy
    );
  };

  const key = (e: HighlightedElement) => e.element; // use direct element reference

  const bMap = new Map(before.map(e => [key(e), e]));
  const aMap = new Map(after.map(e => [key(e), e]));

  const counts = { added: 0, removed: 0, textChanged: 0, attrChanged: 0 };
  const added: CompactChangeItem[] = [];
  const removed: CompactChangeItem[] = [];
  const text: CompactChangeItem[] = [];
  const attr: CompactChangeItem[] = [];

  // removed (viewport check against BEFORE rect)
  for (const [k, be] of bMap) {
    if (!aMap.has(k)) {
      counts.removed++;
      if (inViewport(be.rect) && removed.length < K) {
        const t = norm(be.textContent);
        removed.push({
          type: 'removed',
          selector: be.selector,
          role: be.role ?? undefined,
          hint: t ? `removed "${clip(t)}"` : 'removed',
        });
      }
    }
  }

  // added + changed (viewport check against AFTER rect)
  for (const [k, ae] of aMap) {
    const be = bMap.get(k);
    if (!be) {
      counts.added++;
      if (inViewport(ae.rect) && added.length < K) {
        const t = norm(ae.textContent);
        added.push({
          type: 'added',
          selector: ae.selector,
          role: ae.role ?? undefined,
          hint: t ? `added "${clip(t)}"` : 'added',
        });
      }
      continue;
    }

    // text
    const bt = norm(be.textContent),
      at = norm(ae.textContent);
    if (bt !== at) {
      counts.textChanged++;
      if (inViewport(ae.rect) && text.length < K) {
        text.push({
          type: 'text',
          selector: ae.selector,
          role: ae.role ?? undefined,
          hint: `"${clip(bt)}" → "${clip(at)}"`,
        });
      }
    }

    // attrs (only meaningful ones)
    const ba = be.attributes ?? {},
      aa = ae.attributes ?? {};
    for (const name of new Set([...Object.keys(ba), ...Object.keys(aa)])) {
      if (!MEANINGFUL.has(name)) continue;
      if ((ba[name] ?? '') === (aa[name] ?? '')) continue;
      counts.attrChanged++;
      if (inViewport(ae.rect) && attr.length < K) {
        attr.push({
          type: 'attr',
          selector: ae.selector,
          role: ae.role ?? undefined,
          hint: `attr ${name}`,
        });
      }
    }
  }

  if (added.length === 0 && removed.length === 0 && text.length === 0 && attr.length === 0) {
    return null;
  }

  return {
    type: 'domChange',
    summary: counts,
    highlights: [...added, ...removed, ...text, ...attr],
  };
}
