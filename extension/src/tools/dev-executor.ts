import { Tool<PERSON>allR<PERSON>ult, Tool<PERSON>all, ToolExecutor } from '@the-agent/shared';
import OpenAI from 'openai';
import {
  executeDevToolkit,
  executeWebToolkit,
  parseToolParams,
  toOpenAITools,
} from '~/utils/toolkit';
import { DEV_TOOLKIT_TOOLS, WEB_TOOLKIT_COMMON_TOOLS } from './tool-descriptions';

export class DevToolExecutor implements ToolExecutor {
  async execute(toolCall: ToolCall): Promise<ToolCallResult> {
    if (!toolCall.function.name) {
      throw new Error('Tool name is required');
    }

    const toolName = toolCall.function.name;
    const params = parseToolParams(toolCall);
    if (toolName.startsWith('WebToolkit_')) {
      return await executeWebToolkit(toolName, params);
    } else if (toolName.startsWith('DevToolkit_')) {
      return await executeDevToolkit(toolName, params);
    } else {
      throw new Error(`Unsupported tool call: ${toolName}`);
    }
  }

  getTools(): OpenAI.ChatCompletionTool[] {
    return toOpenAITools([...DEV_TOOLKIT_TOOLS, ...WEB_TOOLKIT_COMMON_TOOLS]);
  }

  getPostToolcallMessage(): string {
    return '';
  }
}
