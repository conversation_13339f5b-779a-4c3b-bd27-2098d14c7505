# Memory Server Agent Architecture

## Overview

The memory server now uses a specialized agent architecture to reduce token usage and improve efficiency. Instead of a single unified agent handling all memory types, we now have three specialized agents.

## Agent Types

### 1. Site Memory Agent (`memory-agent-site`)

- **Purpose**: Handle website-specific interaction patterns
- **System Prompt**: Focused on Q&A format for site interactions
- **Token Usage**: ~200 tokens
- **Use Case**: When `memoryType: 'site'`

### 2. Procedural Memory Agent (`memory-agent-procedural`)

- **Purpose**: Handle step-by-step task execution patterns
- **System Prompt**: Focused on task execution workflows
- **Token Usage**: ~300 tokens
- **Use Case**: When `memoryType: 'procedural'`

### 3. Semantic Memory Agent (`memory-agent-semantic`)

- **Purpose**: Handle conceptual knowledge and definitions
- **System Prompt**: Focused on concept extraction and definition
- **Token Usage**: ~250 tokens
- **Use Case**: When `memoryType: 'semantic'` or default

## Benefits

### Token Efficiency

- **Before**: Every memory operation used ~800 tokens for system prompt
- **After**: Each operation uses only 200-300 tokens based on memory type
- **Savings**: 60-75% reduction in system prompt tokens

### Performance

- Faster processing due to smaller, focused prompts
- Better accuracy due to specialized instructions
- Reduced latency in memory operations

### Maintainability

- Each agent has a clear, focused responsibility
- Easier to optimize prompts for specific use cases
- Better separation of concerns

## Implementation Details

### Agent Selection

The `MemoryService` automatically selects the appropriate agent based on the `memoryType` in the request:

```typescript
private getMemoryAgent(memoryType?: string): ChatAgent {
  switch (memoryType) {
    case 'site':
      return this.siteMemoryAgent;
    case 'procedural':
      return this.proceduralMemoryAgent;
    case 'semantic':
    default:
      return this.semanticMemoryAgent;
  }
}
```

### System Prompts

Each agent has a specialized system prompt:

- **Site Agent**: Focuses on Q&A format and hostname-based grouping
- **Procedural Agent**: Focuses on task execution patterns and workflow/chat modes
- **Semantic Agent**: Focuses on concept extraction and definition formatting (default)

### Tool Usage

All agents use the same tools (`SearchMemory` and `ProcessMemoryOperations`) but with different focus areas and formatting requirements.

## Configuration

The agent selection is automatic based on the `memoryType` field in the memory configuration:

```typescript
// Site memory - uses site agent
{
  metadata: {
    memoryType: 'site',
    hostname: 'example.com'
  }
}

// Procedural memory - uses procedural agent
{
  metadata: {
    memoryType: 'procedural',
    workflowId: 'workflow-123'
  }
}

// Semantic memory - uses semantic agent
{
  metadata: {
    memoryType: 'semantic',
    topic: 'react-development'
  }
}

// No memoryType specified - uses semantic agent (default)
{
  metadata: {
    // No memoryType field
  }
}
```

## Monitoring

The server logs which agents are initialized on startup:

```
Memory agents initialized: site, procedural, semantic
```

You can monitor token usage improvements by comparing system prompt sizes:

- Site Agent: ~200 tokens
- Procedural Agent: ~300 tokens
- Semantic Agent: ~250 tokens (default)
