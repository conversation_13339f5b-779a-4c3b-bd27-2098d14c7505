import express from 'express';
import cors from 'cors';
import OpenAI from 'openai';
import { config } from './config';
import { logger } from './utils/logger';
import { errorHandler } from './api/middleware/error';
import { authMiddleware } from './api/middleware/auth';
import { MemoryService } from './api/services/memory';
import { createMemoryRoutes } from './api/routes/memory';
import {
  createSiteMemoryAgent,
  createProceduralMemoryAgent,
  createSemanticMemoryAgent,
} from './agents/memory';
import { MemoryContextBuilder } from './agents/context';
import { LLMRuntime } from './storage/llm';
import { VectorStore } from './storage/vector';

(async () => {
  const app = express();

  // Middleware
  app.use(cors());
  app.use(express.json());
  // app.use('/memory', authMiddleware);

  // Initialize services
  const llmClient = new OpenAI({
    apiKey: config.openrouter.apiKey,
    baseURL: 'https://openrouter.ai/api/v1',
    defaultHeaders: {
      'HTTP-Referer': 'https://memory-server',
      'X-Title': 'Memory Server',
    },
  });

  const llmRuntime = new LLMRuntime(config.embedding.apiKey, config.embedding.model);
  const vectorStore = new VectorStore();
  const contextBuilder = new MemoryContextBuilder();

  // Create specialized memory agents for different memory types
  const siteMemoryAgent = await createSiteMemoryAgent(
    config.openrouter.model,
    llmClient,
    contextBuilder,
    vectorStore,
    llmRuntime
  );

  const proceduralMemoryAgent = await createProceduralMemoryAgent(
    config.openrouter.model,
    llmClient,
    contextBuilder,
    vectorStore,
    llmRuntime
  );

  const semanticMemoryAgent = await createSemanticMemoryAgent(
    config.openrouter.model,
    llmClient,
    contextBuilder,
    vectorStore,
    llmRuntime
  );

  // Create memory service with specialized agents
  const memoryService = new MemoryService(
    siteMemoryAgent,
    proceduralMemoryAgent,
    semanticMemoryAgent,
    vectorStore,
    llmRuntime
  );

  app.use('/memory', createMemoryRoutes(memoryService));

  // Error handling
  app.use(errorHandler);

  // Start server
  app.listen(config.port, () => {
    logger.info(`Server is running on port ${config.port}`);
    logger.info('Memory agents initialized: site, procedural, semantic');
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    logger.info('SIGTERM received. Shutting down...');
    process.exit(0);
  });
})().catch(error => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});
