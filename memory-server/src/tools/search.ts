import {
  DEFAULT_SEARCH_LIMIT,
  DEFAULT_SEARCH_THRESHOLD,
  TOOL_NAME_SEARCH_MEMORY,
} from '../config/constants';
import { MemoryItem, SearchResult, SearchFilters } from '@the-agent/shared';
import { MemoryContext } from '../types/memory';

export async function searchMemories(
  params: { text: string; limit?: number; filters?: SearchFilters; scoreThreshold?: number },
  context: MemoryContext
): Promise<SearchResult> {
  const { text, limit, filters, scoreThreshold } = params;
  const mergedFilters = { ...(filters ?? {}) };
  if (
    !mergedFilters.userId &&
    !mergedFilters.hostname &&
    !mergedFilters.conversationId &&
    !mergedFilters.workflowId
  ) {
    throw new Error(
      'At least one of userId, hostname, conversationId, or workflowId filter is required!'
    );
  }
  const queryEmbedding = await context.llmRuntime.embed(text);
  const memories = await context.vectorStore.search(
    queryEmbedding,
    limit ?? DEFAULT_SEARCH_LIMIT,
    mergedFilters,
    scoreThreshold ?? DEFAULT_SEARCH_THRESHOLD
  );
  const results: MemoryItem[] = memories.map(mem => ({
    id: mem.id,
    memory: mem.memory,
    score: mem.score,
    metadata: mem.metadata,
  }));
  return { results };
}

export const searchMemoryTool = {
  name: TOOL_NAME_SEARCH_MEMORY,
  description:
    'Search for relevant memories using natural language queries with context filters. Search strategy varies by memory type:\n' +
    '- Site Memory: Search by hostname and UI interaction patterns\n' +
    '- Procedural Memory: Search by execution mode context:\n' +
    '  * Workflow Mode: Search by workflowId + taskId for multi-task workflows\n' +
    '  * Chat Mode: Search by conversationId + agentId for single conversations\n' +
    '- Semantic Memory: Search by user facts, preferences, and personal information:\n' +
    '  * Personal preferences (food, products, activities, entertainment)\n' +
    '  * Professional details (job, work habits, career goals)\n' +
    '  * Plans and intentions (events, trips, goals)\n' +
    '  * Health and wellness preferences\n' +
    '  * Important personal details (names, relationships, dates)\n' +
    'Returns a list of relevant memory facts for deduplication or aggregation.',
  parameters: {
    type: 'object',
    properties: {
      text: {
        type: 'string',
        description:
          'Natural language query to search for relevant memories. For semantic memory, focus on user facts, preferences, and personal information.',
      },
      filters: {
        type: 'object',
        description: 'Filters for search (userId, conversationId, etc.)',
        additionalProperties: true,
      },
      limit: {
        type: 'number',
        description: 'Max results',
        default: 5,
      },
    },
    required: ['text'],
  },
  execute: async (params: any, context: MemoryContext) => {
    return searchMemories(
      { text: params.text, filters: params.filters, limit: params.limit },
      context
    );
  },
};
