import <PERSON><PERSON><PERSON> from 'openai';
import { ChatAgent, AgentConfig } from '@the-agent/shared';
import type { ContextBuilder } from '@the-agent/shared';
import {
  SITE_MEMORY_SYSTEM_PROMPT,
  PROCEDURAL_MEMORY_SYSTEM_PROMPT,
  SEMANTIC_MEMORY_SYSTEM_PROMPT,
} from './prompt';
import { MemoryToolExecutor } from '../tools/executor';
import { VectorStoreInterface, LLMRuntimeInterface } from '../types/memory';
import { DEFAULT_MAX_TOOL_CALLS } from '../config/constants';

export type MemoryAgentType = 'site' | 'procedural' | 'semantic';

// Generic agent creation function
async function createMemoryAgent(
  agentType: MemoryAgentType,
  model: string,
  client: OpenAI,
  contextBuilder: ContextBuilder,
  vectorStore: VectorStoreInterface,
  llmRuntime: LLMRuntimeInterface
): Promise<ChatAgent> {
  const systemPrompts = {
    site: SITE_MEMORY_SYSTEM_PROMPT,
    procedural: PROCEDURAL_MEMORY_SYSTEM_PROMPT,
    semantic: SEMANTIC_MEMORY_SYSTEM_PROMPT,
  };

  const toolExecutor = new MemoryToolExecutor(vectorStore, llmRuntime);

  const config: AgentConfig = {
    id: `memory-agent-${agentType}`,
    model,
    llmClient: client,
    contextBuilder,
    toolExecutor,
    systemPrompt: systemPrompts[agentType],
    maxToolCalls: DEFAULT_MAX_TOOL_CALLS,
  };

  return new ChatAgent(config);
}

// Create specialized Site Memory Agent
export async function createSiteMemoryAgent(
  model: string,
  client: OpenAI,
  contextBuilder: ContextBuilder,
  vectorStore: VectorStoreInterface,
  llmRuntime: LLMRuntimeInterface
): Promise<ChatAgent> {
  return createMemoryAgent('site', model, client, contextBuilder, vectorStore, llmRuntime);
}

// Create specialized Procedural Memory Agent
export async function createProceduralMemoryAgent(
  model: string,
  client: OpenAI,
  contextBuilder: ContextBuilder,
  vectorStore: VectorStoreInterface,
  llmRuntime: LLMRuntimeInterface
): Promise<ChatAgent> {
  return createMemoryAgent('procedural', model, client, contextBuilder, vectorStore, llmRuntime);
}

// Create specialized Semantic Memory Agent
export async function createSemanticMemoryAgent(
  model: string,
  client: OpenAI,
  contextBuilder: ContextBuilder,
  vectorStore: VectorStoreInterface,
  llmRuntime: LLMRuntimeInterface
): Promise<ChatAgent> {
  return createMemoryAgent('semantic', model, client, contextBuilder, vectorStore, llmRuntime);
}
