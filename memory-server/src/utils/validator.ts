import {
  AddMemoryRequest,
  MemoryConfig,
  MemoryMetadata,
  SearchMemoryRequestV2,
} from '@the-agent/shared';
import { MemoryAgentType } from '../agents/memory';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export class MemoryValidator {
  /**
   * Validate add memory request
   */
  static validateAddMemoryRequest(request: AddMemoryRequest): ValidationResult {
    if (!request.messages || !Array.isArray(request.messages) || request.messages.length === 0) {
      return { isValid: false, error: 'messages array is required and must not be empty' };
    }

    if (!request.config?.filters?.userId) {
      return { isValid: false, error: 'userId is required in add memory request' };
    }

    if (request.messages.some(message => !message.conversation_id)) {
      return { isValid: false, error: 'conversation_id is required in messages' };
    }

    // check if all messages have the same conversation_id
    const firstConversationId = request.messages[0].conversation_id;
    if (request.messages.some(message => message.conversation_id !== firstConversationId)) {
      return { isValid: false, error: 'all messages must have the same conversation_id' };
    }

    // Validate memoryType is required and must be one of the valid types
    const memoryType = request.config?.metadata?.memoryType;
    if (!memoryType || typeof memoryType !== 'string') {
      return { isValid: false, error: 'memoryType is required and must be a string' };
    }

    const validMemoryTypes: MemoryAgentType[] = ['site', 'procedural', 'semantic'];
    if (!validMemoryTypes.includes(memoryType as MemoryAgentType)) {
      return { isValid: false, error: `memoryType must be one of: ${validMemoryTypes.join(', ')}` };
    }

    return { isValid: true };
  }

  /**
   * Validate search memory request
   */
  static validateSearchMemoryRequest(request: SearchMemoryRequestV2): ValidationResult {
    if (request.config?.filters?.memoryType !== 'site' && !request.config?.filters?.userId) {
      return { isValid: false, error: 'userId is required in search memory request' };
    }
    if (!request.text || typeof request.text !== 'string') {
      return { isValid: false, error: 'text is required' };
    }

    return { isValid: true };
  }

  /**
   * Validate procedural memory configuration
   */
  static validateProceduralMemoryConfig(config?: MemoryConfig): ValidationResult {
    const metadata = config?.metadata as MemoryMetadata | undefined;
    if (!metadata?.workflowId || !metadata?.agentId || !metadata?.taskId) {
      return {
        isValid: false,
        error: 'workflowId, agentId, and taskId are required for procedural memory',
      };
    }
    return { isValid: true };
  }
}
