import {
  Message,
  MemoryConfig,
  ChatAgent,
  MemoryMetadata,
  extractTextFromContent,
} from '@the-agent/shared';
import { VectorStoreInterface, LLMRuntimeInterface } from '../types/memory';

export function generateUserMessageWithContext(messages: Message[]): string {
  const context = messages.map(formatMessage).join('\n');

  return `\
# 🧠 Conversation Context:
${context}

# 📌 Instructions:
Process the conversation context and extract relevant memories using the appropriate tools.
  `.trim();
}

function formatMessage(msg: Message): string {
  switch (msg.role) {
    case 'user':
      return `user: ${extractTextFromContent(msg.content)}`;

    case 'assistant': {
      const reasoning = (msg as { reasoning?: string }).reasoning;
      const reasoningText = reasoning ? `\nreasoning: ${reasoning}` : '';
      return `assistant: ${msg.content}${reasoningText}`;
    }

    case 'tool': {
      const toolName = (msg as { name?: string }).name || 'unknown_tool';
      const toolCallId = (msg as { tool_call_id?: string }).tool_call_id || '';
      const agentId = (msg as { agent_id?: string }).agent_id || 'unknown';

      const result = extractToolResult(msg.content);

      return `tool[${agentId}:${toolName}${toolCallId ? `:${toolCallId}` : ''}]: ${result}`;
    }

    default:
      if (msg.tool_calls && msg.tool_calls.length > 0) {
        const toolCalls = msg.tool_calls
          .map(tc => {
            const name = tc.function?.name || 'unknown';
            const args = tc.function?.arguments || '{}';
            return `${name}(${args})`;
          })
          .join(', ');
        return `tool_calls: [${toolCalls}]`;
      }

      return JSON.stringify(msg);
  }
}

function extractToolResult(content: string | null | undefined): string {
  if (!content) return 'No result';

  try {
    const parsed = JSON.parse(content);
    return parsed.success ? parsed.data || 'Success' : parsed.error || 'Failed';
  } catch {
    return content || 'No result';
  }
}

export async function processMemoryAsync(
  messages: Message[],
  config: MemoryConfig,
  memoryAgent: ChatAgent
): Promise<void> {
  try {
    await processMemoryWithAgent(messages, config, memoryAgent);
  } catch (error) {
    console.error('Memory processing error:', error);
  }
}

async function processMemoryWithAgent(
  messages: Message[],
  config: MemoryConfig,
  memoryAgent: ChatAgent
): Promise<void> {
  const enhancedContext = generateUserMessageWithContext(messages);
  const userMessage = createUserMessage(enhancedContext, messages);

  await memoryAgent.run(userMessage, {
    toolOptions: {
      filters: config?.filters || {},
      metadata: config?.metadata || {},
    },
  });
}

export async function processRawMemoryAsync(
  data: string,
  metadata: MemoryMetadata,
  vectorStore?: VectorStoreInterface,
  llmRuntime?: LLMRuntimeInterface
): Promise<void> {
  try {
    if (!vectorStore || !llmRuntime) {
      return;
    }

    const embedding = await llmRuntime.embed(data);
    const payload = {
      text: data,
      metadata: {
        ...metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    };

    await vectorStore.insert([embedding], [payload]);
  } catch (error) {
    console.error('Site memory direct processing error:', error);
  }
}

function createUserMessage(content: string, messages: Message[]): Message {
  return {
    id: Date.now(),
    conversation_id: messages[0]?.conversation_id || 0,
    role: 'user',
    content,
  };
}
