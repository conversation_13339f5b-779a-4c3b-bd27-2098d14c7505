import { Request, Response } from 'express';
import {
  AddMemoryRequest,
  SearchMemoryResponse,
  SearchMemoryRequestV2,
  ChatAgent,
  MemoryMetadata,
} from '@the-agent/shared';
import { VectorStoreInterface, LLMRuntimeInterface } from '../../types/memory';
import { MemoryValidator } from '../../utils/validator';
import { processMemoryAsync, processRawMemoryAsync } from '../../utils/memory';
import { MemoryAgentType } from '@/agents/memory';

export class MemoryService {
  private siteMemoryAgent: ChatAgent;
  private proceduralMemoryAgent: ChatAgent;
  private semanticMemoryAgent: ChatAgent;

  constructor(
    siteMemoryAgent: ChatAgent,
    proceduralMemoryAgent: ChatAgent,
    semanticMemoryAgent: ChatAgent,
    private vectorStore: VectorStoreInterface,
    private llmRuntime: LLMRuntimeInterface
  ) {
    this.siteMemoryAgent = siteMemoryAgent;
    this.proceduralMemoryAgent = proceduralMemoryAgent;
    this.semanticMemoryAgent = semanticMemoryAgent;
  }

  /**
   * Get the appropriate memory agent based on memory type
   */
  private getMemoryAgent(memoryType: MemoryAgentType): ChatAgent {
    switch (memoryType) {
      case 'site':
        return this.siteMemoryAgent;
      case 'procedural':
        return this.proceduralMemoryAgent;
      case 'semantic':
        return this.semanticMemoryAgent;
    }
  }

  /**
   * Add memory with preprocessing and validation
   * @param req Request containing messages and config
   * @param res Response object
   */
  async addMemory(req: Request, res: Response): Promise<void> {
    try {
      const requestBody = req.body as AddMemoryRequest;
      const validationResult = MemoryValidator.validateAddMemoryRequest(requestBody);

      if (!validationResult.isValid) {
        res.status(400).json({ error: validationResult.error || 'Validation failed' });
        return;
      }

      const { messages, config } = requestBody;
      const memoryType = config?.metadata?.memoryType as MemoryAgentType;

      res.status(201).json({ results: [], relations: [] });

      // Use the appropriate memory agent based on memory type
      const memoryAgent = this.getMemoryAgent(memoryType);

      processMemoryAsync(messages, config || {}, memoryAgent);
    } catch (error) {
      console.error('addMemory error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Search memories with validation and filtering
   * @param req Request containing search parameters
   * @param res Response object
   */
  async searchMemories(req: Request, res: Response): Promise<void> {
    try {
      const requestBody = req.body as SearchMemoryRequestV2;
      const validationResult = MemoryValidator.validateSearchMemoryRequest(requestBody);

      if (!validationResult.isValid) {
        res.status(400).json({ error: validationResult.error || 'Search validation failed' });
        return;
      }

      const { text, config } = requestBody;

      const queryEmbedding = await this.llmRuntime.embed(text);

      const searchFilters = {
        ...(config?.filters ?? {}),
      };

      if (config?.filters?.memoryType === 'site') {
        // For site memory, remove user-specific filters
        delete searchFilters.userId;
        delete searchFilters.conversationId;
        delete searchFilters.agentId;
        delete searchFilters.runId;
        delete searchFilters.taskId;
        delete searchFilters.workflowId;
      }

      const memories = await this.vectorStore.search(queryEmbedding, config?.limit, searchFilters);

      const response: SearchMemoryResponse = {
        results: memories.map(mem => ({
          id: mem.id,
          memory: mem.memory,
          score: mem.score,
          metadata: mem.metadata,
        })),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('searchMemories error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  async addRawMemory(req: Request, res: Response): Promise<void> {
    try {
      const { data, metadata } = req.body as { data: string; metadata: MemoryMetadata };
      await processRawMemoryAsync(data, metadata, this.vectorStore, this.llmRuntime);
      res.status(200).json({ success: true });
    } catch (error) {
      console.error('addRawMemory error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
