// Simple test script to verify the new agent architecture
const {
  createSiteMemoryAgent,
  createProceduralMemoryAgent,
  createSemanticMemoryAgent,
} = require('./dist/agents/memory');

async function testAgents() {
  console.log('Testing memory agent creation...');

  try {
    // Test that all agent types can be created
    const siteAgent = await createSiteMemoryAgent('test-model', {}, {}, {}, {});
    console.log('✅ Site memory agent created successfully');

    const proceduralAgent = await createProceduralMemoryAgent('test-model', {}, {}, {}, {});
    console.log('✅ Procedural memory agent created successfully');

    const semanticAgent = await createSemanticMemoryAgent('test-model', {}, {}, {}, {});
    console.log('✅ Semantic memory agent created successfully');

    console.log('\n🎉 All memory agents created successfully!');
    console.log('Agent IDs:');
    console.log('- Site:', siteAgent.config.id);
    console.log('- Procedural:', proceduralAgent.config.id);
    console.log('- Semantic:', semanticAgent.config.id);
  } catch (error) {
    console.error('❌ Error creating agents:', error);
  }
}

testAgents();
