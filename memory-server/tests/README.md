# Memory Server Tests

This directory contains all test files for the memory-server.

## Test File Descriptions

### API Tests

- `test-all-apis.js` - Comprehensive test for all API endpoints
- `check-stored-memories.js` - Test memory search functionality with detailed output
- `test-add-new.js` - Test add memory functionality
- `test-delete.js` - Test delete memory functionality
- `test-get-memory.js` - Test get single memory functionality
- `test-get-all.js` - Test get all memories functionality
- `test-search-after-delete.js` - Test search after delete functionality

### Procedural Memory Tests

- `test-procedural-memory.js` - Test procedural memory functionality with hybrid strategy
- `test-hybrid-strategy.js` - Test procedural memory hybrid optimization strategy
- `test-memory-types.js` - Test memory type classification and search priority

### Infrastructure Tests

- `test-supabase.js` - Test Supabase connection and table structure

## Running Tests

### Run All Tests

```bash
cd tests
node run-tests.js all
```

### Run a Single Test

```bash
cd tests
node run-tests.js check-stored-memories
node run-tests.js test-procedural-memory
node run-tests.js test-hybrid-strategy
node run-tests.js test-memory-types
```

### List Available Tests

```bash
cd tests
node run-tests.js
```

## Test Environment Requirements

1. **Server Running**: Ensure memory-server is running on port 3000
2. **Environment Variables**: Ensure `.env` file is configured correctly
3. **Database Connection**: Ensure Supabase connection is working

## Test Data

The following user ID is used for testing:

```
48a02574f02e8dad07b7c3478ab81a042a59054a46a50fa15bed63e8270cec52
```

## Test Coverage

### Core API Tests

- ✅ Search memory API
- ✅ Add memory API
- ✅ Delete memory API
- ✅ Get single memory API
- ✅ Get all memories API
- ✅ User permission validation
- ✅ Error handling

### Procedural Memory Tests

- ✅ Simple procedural memory addition
- ✅ Procedural memory with failed tools
- ✅ Complex tool results extraction
- ✅ Workflow mode vs Chat mode
- ✅ Mixed message types (semantic + procedural)
- ✅ Memory format verification

### Hybrid Strategy Tests

- ✅ Simple deduplication (no duplicate found)
- ✅ Exact duplicate detection
- ✅ High similarity detection
- ✅ Cache mechanism performance
- ✅ Fallback to LLM agent on error
- ✅ Search results verification

### Memory Type Classification Tests

- ✅ Site memory classification and search
- ✅ Semantic memory classification and search
- ✅ Procedural memory (workflow mode) classification and search
- ✅ Procedural memory (chat mode) classification and search
- ✅ Mixed memory type search
- ✅ Search priority verification
- ✅ Memory type validation

### Infrastructure Tests

- ✅ Supabase connection test

## Procedural Memory Test Scenarios

### Basic Functionality

- Tool message processing
- Status extraction (Success/Failed)
- Result extraction (URLs, selectors, content lengths)
- Format validation

### Hybrid Strategy

- **Simple Deduplication**: Low-cost duplicate check using exact content match
- **LLM Agent Fallback**: Intelligent aggregation when duplicates found
- **Cache Performance**: Verify cache mechanism reduces processing time
- **Error Handling**: Fallback to LLM agent when optimization fails

### Search and Retrieval

- Workflow mode search (workflowId + taskId)
- Chat mode search (conversationId + agentId)
- Mixed memory type search
- Memory format verification

## Memory Type Classification Test Scenarios

### Site Memory

- **Classification**: Q&A format for website interactions
- **Search**: By hostname, no userId required
- **Priority**: Hostname match first, then similarity

### Semantic Memory

- **Classification**: Conceptual knowledge and definitions
- **Search**: By topic/concept and content
- **Priority**: Topic/concept match first, then similarity

### Procedural Memory

- **Workflow Mode**: Grouped by workflowId + taskId
- **Chat Mode**: Grouped by conversationId + agentId
- **Search Priority**: Execution context match first, then similarity
- **Format**: `Task: [taskGoal] | [tool1:status(result)] | [tool2:status(result)] | ...`

## Troubleshooting

If a test fails, please check:

1. Whether the server is running
2. Whether environment variables are configured correctly
3. Whether Supabase connection is working
4. Whether the network connection is normal
5. Whether the memory agent is properly configured for LLM processing

### Common Issues

- **Procedural memory tests failing**: Check if the memory agent is running and LLM is configured
- **Cache tests failing**: This is expected if the cache is not implemented or not working
- **Search tests failing**: Check if the vector store is properly configured
- **Memory type tests failing**: Check if the memory type classification is working correctly
