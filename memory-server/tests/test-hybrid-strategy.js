const axios = require('axios');
const assert = require('assert');

const BASE = 'http://localhost:3000/memory';

async function run() {
  console.log('🧪 Testing Procedural Memory Hybrid Strategy...\n');

  // Test 1: Simple deduplication (no duplicate found - should store directly)
  console.log('1. Testing simple deduplication - no duplicate found...');
  let res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 1,
        conversation_id: 1753172353001,
        role: 'tool',
        name: 'WebToolkit_openTab',
        content: JSON.stringify({
          success: true,
          data: {
            tabId: 200,
            url: 'https://www.unique-site.com',
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-hybrid-1',
        conversationId: '1753172353001',
        agentId: 'browser-agent-hybrid-1',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-hybrid-1',
        taskId: 'task-hybrid-1',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Simple deduplication - no duplicate passed');

  // Test 2: Exact duplicate (should trigger LLM agent)
  console.log('\n2. Testing exact duplicate - should trigger LLM agent...');
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 2,
        conversation_id: 1753172353002,
        role: 'tool',
        name: 'WebToolkit_openTab',
        content: JSON.stringify({
          success: true,
          data: {
            tabId: 201,
            url: 'https://www.duplicate-site.com',
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-hybrid-2',
        conversationId: '1753172353002',
        agentId: 'browser-agent-hybrid-2',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-hybrid-2',
        taskId: 'task-hybrid-2',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ First addition passed');

  // Add the same content again to trigger duplicate detection
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 3,
        conversation_id: 1753172353003,
        role: 'tool',
        name: 'WebToolkit_openTab',
        content: JSON.stringify({
          success: true,
          data: {
            tabId: 202,
            url: 'https://www.duplicate-site.com',
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-hybrid-2',
        conversationId: '1753172353003',
        agentId: 'browser-agent-hybrid-2',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-hybrid-2',
        taskId: 'task-hybrid-2',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Duplicate detection passed');

  // Test 3: High similarity (should trigger LLM agent)
  console.log('\n3. Testing high similarity - should trigger LLM agent...');
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 4,
        conversation_id: 1753172353004,
        role: 'tool',
        name: 'WebToolkit_openTab',
        content: JSON.stringify({
          success: true,
          data: {
            tabId: 203,
            url: 'https://www.similar-site.com',
          },
        }),
        status: 'completed',
      },
      {
        id: 5,
        conversation_id: 1753172353004,
        role: 'tool',
        name: 'WebToolkit_navigate',
        content: JSON.stringify({
          success: true,
          data: {
            url: 'https://www.similar-site.com',
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-hybrid-3',
        conversationId: '1753172353004',
        agentId: 'browser-agent-hybrid-3',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-hybrid-3',
        taskId: 'task-hybrid-3',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ First similar addition passed');

  // Add similar content to trigger similarity detection
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 6,
        conversation_id: 1753172353005,
        role: 'tool',
        name: 'WebToolkit_openTab',
        content: JSON.stringify({
          success: true,
          data: {
            tabId: 204,
            url: 'https://www.similar-site-2.com',
          },
        }),
        status: 'completed',
      },
      {
        id: 7,
        conversation_id: 1753172353005,
        role: 'tool',
        name: 'WebToolkit_navigate',
        content: JSON.stringify({
          success: true,
          data: {
            url: 'https://www.similar-site-2.com',
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-hybrid-3',
        conversationId: '1753172353005',
        agentId: 'browser-agent-hybrid-3',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-hybrid-3',
        taskId: 'task-hybrid-3',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Similarity detection passed');

  // Test 4: Cache mechanism test
  console.log('\n4. Testing cache mechanism...');
  const startTime = Date.now();

  // First call
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 8,
        conversation_id: 1753172353006,
        role: 'tool',
        name: 'WebToolkit_click',
        content: JSON.stringify({
          success: true,
          data: 'clicked',
          action: {
            tool: 'click',
            args: {
              selectorOrIndex: 'button.test',
            },
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-hybrid-4',
        conversationId: '1753172353006',
        agentId: 'browser-agent-hybrid-4',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-hybrid-4',
        taskId: 'task-hybrid-4',
      },
    },
  });
  const firstCallTime = Date.now() - startTime;
  assert.strictEqual(res.status, 201);
  console.log(`✅ First call completed in ${firstCallTime}ms`);

  // Second call with same content (should use cache)
  const cacheStartTime = Date.now();
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 9,
        conversation_id: 1753172353007,
        role: 'tool',
        name: 'WebToolkit_click',
        content: JSON.stringify({
          success: true,
          data: 'clicked',
          action: {
            tool: 'click',
            args: {
              selectorOrIndex: 'button.test',
            },
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-hybrid-4',
        conversationId: '1753172353007',
        agentId: 'browser-agent-hybrid-4',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-hybrid-4',
        taskId: 'task-hybrid-4',
      },
    },
  });
  const cacheCallTime = Date.now() - cacheStartTime;
  assert.strictEqual(res.status, 201);
  console.log(`✅ Cached call completed in ${cacheCallTime}ms`);

  if (cacheCallTime < firstCallTime) {
    console.log('✅ Cache mechanism working (cached call faster)');
  } else {
    console.log('⚠️ Cache mechanism may not be working (cached call not faster)');
  }

  // Test 5: Fallback to LLM agent on error
  console.log('\n5. Testing fallback to LLM agent on error...');
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 10,
        conversation_id: 1753172353008,
        role: 'tool',
        name: 'WebToolkit_invalidTool',
        content: JSON.stringify({
          success: false,
          error: 'Invalid tool call',
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-hybrid-5',
        conversationId: '1753172353008',
        agentId: 'browser-agent-hybrid-5',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-hybrid-5',
        taskId: 'task-hybrid-5',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Fallback mechanism passed');

  // Test 6: Search results verification
  console.log('\n6. Testing search results verification...');
  res = await axios.post(`${BASE}/search`, {
    text: 'openTab navigate',
    config: {
      filters: {
        userId: 'test-hybrid-3',
        workflowId: 'workflow-hybrid-3',
        memoryType: 'procedural',
      },
      limit: 5,
    },
  });
  assert.strictEqual(res.status, 200);
  assert(Array.isArray(res.data.results), 'results should be an array');
  console.log(`Found ${res.data.results.length} procedural memories`);
  res.data.results.forEach((result, index) => {
    console.log(`Memory ${index + 1}:`, result.memory.substring(0, 80) + '...');
  });
  console.log('✅ Search results verification passed');

  // Test 7: Memory format verification
  console.log('\n7. Testing memory format verification...');
  res = await axios.post(`${BASE}/search`, {
    text: 'click',
    config: {
      filters: {
        userId: 'test-hybrid-4',
        memoryType: 'procedural',
      },
      limit: 3,
    },
  });
  assert.strictEqual(res.status, 200);
  if (res.data.results.length > 0) {
    const memory = res.data.results[0].memory;
    console.log('Memory format:', memory);

    // Verify the format matches our expected pattern
    const formatPattern = /^Task: .+ \| .+:.+\(.+\)/;
    if (formatPattern.test(memory)) {
      console.log('✅ Memory format matches expected pattern');
    } else {
      console.log('⚠️ Memory format does not match expected pattern');
    }
  }
  console.log('✅ Memory format verification passed');

  console.log(
    '\n🎉 All hybrid strategy tests passed! Procedural memory optimization is working correctly.'
  );
}

run().catch(e => {
  console.error('❌ Test failed:', e.message);
  if (e.response) {
    console.error('Response status:', e.response.status);
    console.error('Response data:', e.response.data);
  }
  process.exit(1);
});
