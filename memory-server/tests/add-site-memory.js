const axios = require('axios');

const siteJson = [
  {
    site: 'https://x.com',
    faqs: [
      {
        question: 'How to scroll down on https://x.com',
        answer:
          'To scroll down the page, use the WebToolkit_sendKeys tool with the Space key. This will scroll down one page at a time. WebToolkit_sendKeys({"keys":"Space"})',
      },
      {
        question: 'How to search for content on X.com',
        answer: `To search for content on X.com, use the WebToolkit_input tool with the search field selector. WebToolkit_input({"selectorOrIndex":"[data-testid='SearchBox_Search_Input']","value":"?","options":{"pressEnterAfterInput":true}})`,
      },
      {
        question: 'How to write a post on X.com',
        answer: `To write a post on X.com, use the WebToolkit_input tool with the tweet textarea selector. WebToolkit_input({"selectorOrIndex":"textarea[data-testid='tweetTextarea_0']","value":"?"})`,
      },
      {
        question: 'How to click post button on X.com',
        answer: `To click the post button on X.com, use the WebToolkit_click tool with the appropriate selector. For inline posts use [data-testid='tweetButtonInline'], for popup posts use [data-testid='tweetButton']. Must be executed after input.`,
      },
      {
        question: 'How to follow a user on their profile page',
        answer: `To follow a user on their profile page, use the WebToolkit_click tool with the css selector of the follow button. WebToolkit_click({"selectorOrIndex":"[aria-label^='Follow @']"})`,
      },
      {
        question: 'How to reply to a post on X.com',
        answer: `To reply to a post on X.com, first use WebToolkit_input with textarea[data-testid='tweetTextarea_0'], then submit with WebToolkit_click using [data-testid='tweetButtonInline'].`,
      },
      {
        question: 'How to interact with posts on X.com',
        answer: `To interact with posts on X.com: like using [data-testid='like'], bookmark using [data-testid='bookmark'], follow users using [aria-label^='Follow @'].`,
      },
      {
        question: 'How to find content sections on X.com',
        answer: `To find content sections on X.com, use WebToolkit_analyzePageDOM with appropriate selectors: posts ([aria-label='Timeline: Your Home Timeline']), comments ([aria-label='Timeline: Conversation']).`,
      },
      {
        question: 'How to navigate to messages page on https://x.com',
        answer: `To navigate to the messages page, use the WebToolkit_click tool with the Direct Messages link selector in the left sidebar. WebToolkit_click({"selectorOrIndex":"[data-testid='AppTabBar_DirectMessage_Link']"})`,
      },

      {
        question: 'How to interact with private messages on X.com',
        answer: `To interact with private messages on X.com: find messages using WebToolkit_analyzePageDOM with [aria-label='Timeline: Messages'], switch users using [data-testid='conversation'], search users using input[placeholder='Search Direct Messages'], write messages using [data-testid='dmComposerTextInput'].`,
      },
      {
        question: 'How to open a new tab for https://x.com',
        answer: `To open a new tab for X.com, use the TabToolkit_openTab tool with the URL. TabToolkit_openTab({"url":"https://x.com"})`,
      },
      {
        question: 'How to create a new post on X.com',
        answer: `To create a new post on X.com, either click the Post button in the sidebar (WebToolkit_click({"selectorOrIndex":"[data-testid='SideNav_NewTweet_Button']"})) or navigate directly to the compose page (TabToolkit_openTab({"url":"https://x.com/compose/post"})).`,
      },
      {
        question: 'How to navigate to explore page via URL',
        answer: `To navigate to the explore page directly, use the TabToolkit_openTab tool with the explore URL. TabToolkit_openTab({"url":"https://x.com/explore"})`,
      },
      {
        question: 'How to close the current tab',
        answer: `To close the current tab, first get the current tab ID using TabToolkit_getCurrentActiveTab, then use TabToolkit_closeTab with the tab ID. TabToolkit_closeTab({"tabId":tabId})`,
      },
      {
        question: 'How to use common web tools on X.com',
        answer: `Common web tools on X.com: extract text (WebToolkit_extractText), take screenshot (WebToolkit_screenshot), refresh page (WebToolkit_refreshPage), scroll to element (WebToolkit_scrollTo).`,
      },
    ],
  },
  {
    site: 'https://www.xiaohongshu.com/',
    faqs: [
      {
        question: 'How to scroll down on https://www.xiaohongshu.com',
        answer: `To scroll down the page, use the WebToolkit_sendKeys tool with the Space key to scroll down one screen. WebToolkit_sendKeys({"keys":"Space"})`,
      },
      {
        question: 'How to search for content on https://www.xiaohongshu.com/explore',
        answer: `To search for content, use the WebToolkit_input tool with the search field selector. WebToolkit_input({"selectorOrIndex":"#search-input","value":"?","options":{"pressEnterAfterInput":true}})`,
      },
      {
        question:
          'How to view user profile from post detail page on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To view the user profile from a post detail page, use the WebToolkit_click tool with the css selector of the profile button. WebToolkit_click({"selectorOrIndex":".author-wrapper a"})`,
      },

      {
        question: 'How to close post detail page on https://www.xiaohongshu.com/explore/{post_id}',
        answer:
          'To close the post detail page, use the WebToolkit_click tool with the css selector of the close button. WebToolkit_click({"selectorOrIndex":".close-circle"})',
      },
      {
        question: 'How to reply to a post on Xiaohongshu',
        answer: `To reply to a post on Xiaohongshu, first use WebToolkit_input with #content-textarea, then submit with WebToolkit_click using .btn.submit selector.`,
      },
      {
        question: 'How to interact with posts on Xiaohongshu',
        answer: `To interact with posts on Xiaohongshu: like using .like-wrapper.like-active, collect using #note-page-collect-board-guide, view comments using .chat-wrapper, follow users using .note-detail-follow-btn button.`,
      },
      {
        question: 'How to find Posts on https://www.xiaohongshu.com/explore',
        answer: `To find Posts, use the WebToolkit_analyzePageDOM tool. You'll need the selector of the post field. WebToolkit_analyzePageDOM({"selector":"#mfContainer"})`,
      },
      {
        question: 'How to find Comments on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To find Comments, use the WebToolkit_analyzePageDOM tool. You'll need the selector of the comment field. WebToolkit_analyzePageDOM({"selector":".comment-item"})`,
      },
      {
        question: 'How to find Notifications on https://www.xiaohongshu.com/notification',
        answer: `To find Notifications, use the WebToolkit_analyzePageDOM tool. You'll need the selector of the notification field. WebToolkit_analyzePageDOM({"selector":".tabs-content-container"})`,
      },
      {
        question: 'How to open a new tab for https://www.xiaohongshu.com',
        answer: `To open a new tab for Xiaohongshu, use the TabToolkit_openTab tool with the URL. TabToolkit_openTab({"url":"https://www.xiaohongshu.com"})`,
      },
      {
        question: 'How to use common web tools on Xiaohongshu',
        answer: `Common web tools on Xiaohongshu: extract text (WebToolkit_extractText), take screenshot (WebToolkit_screenshot), refresh page (WebToolkit_refreshPage), scroll to element (WebToolkit_scrollTo).`,
      },
    ],
  },
];

// 配置
const MEMORY_SERVER_URL = 'http://localhost:3000/memory';
const BATCH_SIZE = 10; // 每批处理的数量
const DELAY_BETWEEN_BATCHES = 1000; // 批次间延迟（毫秒）

// 转换数据格式
function transformData(siteData) {
  const memories = [];

  siteData.forEach(site => {
    // 确保 URL 有正确的协议
    let siteUrl = site.site;
    if (!siteUrl.startsWith('http://') && !siteUrl.startsWith('https://')) {
      siteUrl = 'https://' + siteUrl;
    }

    const hostname = new URL(siteUrl).hostname;

    site.faqs.forEach(faq => {
      const memory = {
        data: `Q: ${faq.question}\n\nA: ${faq.answer}`,
        metadata: {
          site: siteUrl,
          hostname: hostname,
          memoryType: 'site',
        },
      };
      memories.push(memory);
    });
  });

  return memories;
}

// 发送单个 memory 请求
async function addMemory(memoryData) {
  try {
    console.log('📤 Sending raw memory:', JSON.stringify(memoryData, null, 2));

    const response = await axios.post(`${MEMORY_SERVER_URL}/add_raw`, memoryData);
    console.log('✅ Memory added successfully:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Failed to add memory:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

// 批量处理
async function processBatch(memories, startIndex) {
  const batch = memories.slice(startIndex, startIndex + BATCH_SIZE);
  console.log(
    `\n📦 Processing batch ${Math.floor(startIndex / BATCH_SIZE) + 1} (${batch.length} items)`
  );

  const promises = batch.map(async (memory, index) => {
    const questionText = memory.data.split('\n')[0].replace('Q: ', '');
    console.log(
      `  ${startIndex + index + 1}. Adding memory for: ${memory.metadata.hostname} - ${questionText.substring(0, 50)}...`
    );
    const result = await addMemory(memory);
    return result;
  });

  const results = await Promise.all(promises);
  const successCount = results.filter(result => result).length;
  const failCount = results.length - successCount;

  console.log(`  ✅ Success: ${successCount}, ❌ Failed: ${failCount}`);
  return { successCount, failCount };
}

// 主函数
async function main() {
  console.log('🚀 Starting to update site memories...');
  console.log(`📊 Total sites: ${siteJson.length}`);

  const totalFaqs = siteJson.reduce((sum, site) => sum + site.faqs.length, 0);
  console.log(`📊 Total FAQs: ${totalFaqs}`);

  // 转换数据
  const memories = transformData(siteJson);
  console.log(`🔄 Transformed ${memories.length} memories`);

  // 获取所有唯一的 hostname
  const hostnames = [...new Set(memories.map(memory => memory.metadata.hostname))];
  console.log(`🌐 Hostnames to update: ${hostnames.join(', ')}`);

  console.log('\n📝 Note: This script will add new memories. If you need to delete old ones,');
  console.log('   you may need to manually delete them or implement a delete API endpoint.');

  let totalSuccess = 0;
  let totalFailed = 0;

  // 分批处理
  for (let i = 0; i < memories.length; i += BATCH_SIZE) {
    const { successCount, failCount } = await processBatch(memories, i);
    totalSuccess += successCount;
    totalFailed += failCount;

    // 如果不是最后一批，添加延迟
    if (i + BATCH_SIZE < memories.length) {
      console.log(`⏳ Waiting ${DELAY_BETWEEN_BATCHES}ms before next batch...`);
      await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES));
    }
  }

  console.log('\n🎉 Update completed!');
  console.log(`📊 Summary:`);
  console.log(`  📤 Memories added: ${totalSuccess}/${memories.length}`);
  console.log(`  ❌ Failed to add: ${totalFailed}`);
  console.log(`  📈 Success rate: ${((totalSuccess / memories.length) * 100).toFixed(2)}%`);
}

// 运行脚本
main().catch(console.error);
