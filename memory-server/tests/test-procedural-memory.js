const axios = require('axios');
const assert = require('assert');

const BASE = 'http://localhost:3000/memory';

async function run() {
  console.log('🧪 Testing Procedural Memory API (Hybrid Strategy)...\n');

  // Test 1: Simple procedural memory (tool messages only)
  console.log('1. Testing simple procedural memory with tool messages...');
  let res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 1,
        conversation_id: 1753172352001,
        role: 'tool',
        name: 'WebToolkit_openTab',
        content: JSON.stringify({
          success: true,
          data: {
            tabId: 123,
            url: 'https://www.baidu.com',
          },
          context: {
            tabId: 123,
            pageId: 1,
            url: 'https://www.baidu.com',
            title: '百度一下，你就知道',
          },
        }),
        status: 'completed',
      },
      {
        id: 2,
        conversation_id: 1753172352001,
        role: 'tool',
        name: 'WebToolkit_navigate',
        content: JSON.stringify({
          success: true,
          data: {
            url: 'https://www.baidu.com',
          },
          context: {
            tabId: 123,
            pageId: 1,
            url: 'https://www.baidu.com',
          },
        }),
        status: 'completed',
      },
      {
        id: 3,
        conversation_id: 1753172352001,
        role: 'tool',
        name: 'WebToolkit_click',
        content: JSON.stringify({
          success: true,
          data: 'clicked',
          context: {
            tabId: 123,
            pageId: 1,
            url: 'https://www.baidu.com',
          },
          action: {
            tool: 'click',
            args: {
              selectorOrIndex: 'button.submit',
            },
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-user-procedural-1',
        conversationId: '1753172352001',
        agentId: 'browser-agent-1',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-test-1',
        taskId: 'task-test-1',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Simple procedural memory addition passed');

  // Test 2: Search for procedural memory
  console.log('\n2. Testing procedural memory search...');
  res = await axios.post(`${BASE}/search`, {
    text: 'openTab navigate click',
    config: {
      filters: {
        userId: 'test-user-procedural-1',
        conversationId: '1753172352001',
        memoryType: 'procedural',
      },
      limit: 5,
    },
  });
  assert.strictEqual(res.status, 200);
  assert(Array.isArray(res.data.results), 'results should be an array');
  if (res.data.results.length > 0) {
    console.log('Found procedural memory:', res.data.results[0].memory);
  }
  console.log('✅ Procedural memory search passed');

  // Test 3: Procedural memory with failed tool
  console.log('\n3. Testing procedural memory with failed tool...');
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 4,
        conversation_id: 1753172352002,
        role: 'tool',
        name: 'WebToolkit_click',
        content: JSON.stringify({
          success: true,
          data: 'clicked',
          context: {
            tabId: 124,
            pageId: 1,
            url: 'https://www.google.com',
          },
          action: {
            tool: 'click',
            args: {
              selectorOrIndex: 'button.visible',
            },
          },
        }),
        status: 'completed',
      },
      {
        id: 5,
        conversation_id: 1753172352002,
        role: 'tool',
        name: 'WebToolkit_click',
        content: JSON.stringify({
          success: false,
          error: 'Element not found: button.not_found',
          context: {
            tabId: 124,
            pageId: 1,
            url: 'https://www.google.com',
          },
          action: {
            tool: 'click',
            args: {
              selectorOrIndex: 'button.not_found',
            },
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-user-procedural-2',
        conversationId: '1753172352002',
        agentId: 'browser-agent-2',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-test-2',
        taskId: 'task-test-2',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Procedural memory with failed tool passed');

  // Test 4: Procedural memory with complex tool results
  console.log('\n4. Testing procedural memory with complex tool results...');
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 6,
        conversation_id: 1753172352003,
        role: 'tool',
        name: 'WebToolkit_analyzePageDOM',
        content: JSON.stringify({
          success: true,
          data: {
            domTree:
              'form#loginForm (action="/login", method="post")\n  - input#username [label="Username", type="text", required]\n  - button.submit (selector="form > button.submit") [label="Login", role="button"]',
          },
          context: {
            tabId: 125,
            pageId: 1,
            url: 'https://www.example.com',
            title: 'Login Page',
          },
          action: {
            tool: 'analyzePageDOM',
            args: {
              selector: 'form',
            },
          },
        }),
        status: 'completed',
      },
      {
        id: 7,
        conversation_id: 1753172352003,
        role: 'tool',
        name: 'WebToolkit_extractText',
        content: JSON.stringify({
          success: true,
          data: {
            content: 'Welcome to our website. Please login to continue.',
            title: 'Login Page',
          },
          context: {
            tabId: 125,
            pageId: 1,
            url: 'https://www.example.com',
          },
          action: {
            tool: 'extractText',
            args: {},
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-user-procedural-3',
        conversationId: '1753172352003',
        agentId: 'browser-agent-3',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-test-3',
        taskId: 'task-test-3',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Procedural memory with complex tool results passed');

  // Test 5: Search for procedural memory by workflow context
  console.log('\n5. Testing procedural memory search by workflow context...');
  res = await axios.post(`${BASE}/search`, {
    text: 'analyze page extract text',
    config: {
      filters: {
        userId: 'test-user-procedural-3',
        workflowId: 'workflow-test-3',
        taskId: 'task-test-3',
        memoryType: 'procedural',
      },
      limit: 5,
    },
  });
  assert.strictEqual(res.status, 200);
  assert(Array.isArray(res.data.results), 'results should be an array');
  if (res.data.results.length > 0) {
    console.log('Found workflow procedural memory:', res.data.results[0].memory);
  }
  console.log('✅ Workflow context search passed');

  // Test 6: Chat mode procedural memory (no workflowId)
  console.log('\n6. Testing chat mode procedural memory...');
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 8,
        conversation_id: 1753172352004,
        role: 'tool',
        name: 'WebToolkit_openTab',
        content: JSON.stringify({
          success: true,
          data: {
            tabId: 126,
            url: 'https://www.github.com',
          },
          context: {
            tabId: 126,
            pageId: 1,
            url: 'https://www.github.com',
            title: 'GitHub',
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-user-procedural-4',
        conversationId: '1753172352004',
        agentId: 'browser-agent-4',
      },
      metadata: {
        memoryType: 'procedural',
        // No workflowId - chat mode
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Chat mode procedural memory passed');

  // Test 7: Search for chat mode procedural memory
  console.log('\n7. Testing chat mode procedural memory search...');
  res = await axios.post(`${BASE}/search`, {
    text: 'openTab',
    config: {
      filters: {
        userId: 'test-user-procedural-4',
        conversationId: '1753172352004',
        agentId: 'browser-agent-4',
        memoryType: 'procedural',
      },
      limit: 5,
    },
  });
  assert.strictEqual(res.status, 200);
  assert(Array.isArray(res.data.results), 'results should be an array');
  if (res.data.results.length > 0) {
    console.log('Found chat mode procedural memory:', res.data.results[0].memory);
  }
  console.log('✅ Chat mode search passed');

  // Test 8: Mixed message types (should separate semantic and procedural)
  console.log('\n8. Testing mixed message types...');
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 9,
        conversation_id: 1753172352005,
        role: 'user',
        content: 'I want to search for information about React',
        status: 'completed',
      },
      {
        id: 10,
        conversation_id: 1753172352005,
        role: 'assistant',
        content: 'I will help you search for React information',
        status: 'completed',
      },
      {
        id: 11,
        conversation_id: 1753172352005,
        role: 'tool',
        name: 'WebToolkit_openTab',
        content: JSON.stringify({
          success: true,
          data: {
            tabId: 127,
            url: 'https://reactjs.org',
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-user-procedural-5',
        conversationId: '1753172352005',
        agentId: 'browser-agent-5',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-test-5',
        taskId: 'task-test-5',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Mixed message types passed');

  // Test 9: Search for both semantic and procedural memories
  console.log('\n9. Testing search for both memory types...');
  res = await axios.post(`${BASE}/search`, {
    text: 'React search',
    config: {
      filters: {
        userId: 'test-user-procedural-5',
        conversationId: '1753172352005',
      },
      limit: 10,
    },
  });
  assert.strictEqual(res.status, 200);
  assert(Array.isArray(res.data.results), 'results should be an array');
  console.log(`Found ${res.data.results.length} memories`);
  res.data.results.forEach((result, index) => {
    console.log(
      `Memory ${index + 1} (${result.metadata?.memoryType}):`,
      result.memory.substring(0, 100) + '...'
    );
  });
  console.log('✅ Mixed memory type search passed');

  console.log('\n🎉 All procedural memory tests passed! Hybrid strategy is working correctly.');
}

run().catch(e => {
  console.error('❌ Test failed:', e.message);
  if (e.response) {
    console.error('Response status:', e.response.status);
    console.error('Response data:', e.response.data);
  }
  process.exit(1);
});
