const axios = require('axios');
const assert = require('assert');

const BASE = 'http://localhost:3000/memory';

async function testEndToEndMemory() {
  console.log('🧪 Testing End-to-End Memory Storage (Extension Style)...\n');

  // Test 1: Add semantic memory (user + assistant messages only)
  console.log('1. Adding semantic memory (user + assistant messages)...');
  try {
    const addRes = await axios.post(`${BASE}/add`, {
      messages: [
        // User message
        {
          role: 'user',
          content: 'How do I open a new tab and navigate to Google?',
          conversation_id: '1753172354003',
          agent_id: 'browser-agent-real',
          task_id: 'task-real-1',
          run_id: 'run-real-1',
        },
        // Assistant message
        {
          role: 'assistant',
          content: "I'll help you open a new tab and navigate to Google. Let me do that for you.",
          conversation_id: '1753172354003',
          agent_id: 'browser-agent-real',
          task_id: 'task-real-1',
          run_id: 'run-real-1',
        },
      ],
      config: {
        filters: {
          userId: 'test-real-user',
          conversationId: '1753172354003',
        },
        metadata: {
          memoryType: 'semantic',
          userId: 'test-real-user',
          conversationId: '1753172354003',
          agentId: 'browser-agent-real',
          taskId: 'task-real-1',
          runId: 'run-real-1',
          workflowId: 'workflow-real-1',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      },
    });
    console.log('✅ Added semantic memory');
  } catch (e) {
    console.log('❌ Error adding semantic:', e.response?.data || e.message);
  }

  // Test 2: Add procedural memory (tool messages only)
  console.log('\n2. Adding procedural memory (tool messages only)...');
  try {
    const addRes = await axios.post(`${BASE}/add`, {
      messages: [
        // Tool message
        {
          role: 'tool',
          name: 'WebToolkit_openTab',
          content: '{"success":true,"data":{"url":"https://www.google.com","tabId":123}}',
          conversation_id: '1753172354003',
          agent_id: 'browser-agent-real',
          task_id: 'task-real-1',
          run_id: 'run-real-1',
        },
      ],
      config: {
        filters: {
          userId: 'test-real-user',
          conversationId: '1753172354003',
          agentId: 'browser-agent-real',
          taskId: 'task-real-1',
          workflowId: 'workflow-real-1',
        },
        metadata: {
          memoryType: 'procedural',
          userId: 'test-real-user',
          conversationId: '1753172354003',
          agentId: 'browser-agent-real',
          taskId: 'task-real-1',
          runId: 'run-real-1',
          workflowId: 'workflow-real-1',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      },
    });
    console.log('✅ Added procedural memory');
  } catch (e) {
    console.log('❌ Error adding procedural:', e.response?.data || e.message);
  }

  // Test 3: Search for semantic memory
  console.log('\n3. Searching for semantic memory...');
  try {
    const searchRes = await axios.post(`${BASE}/search`, {
      text: 'open new tab navigate Google',
      config: {
        filters: {
          userId: 'test-real-user',
          conversationId: '1753172354003',
          memoryType: 'semantic',
        },
        limit: 5,
      },
    });
    console.log('Semantic search results:', searchRes.data.results.length);
    searchRes.data.results.forEach((r, i) => {
      const memoryType = r.metadata?.memoryType || 'unknown';
      console.log(
        `  ${i + 1}. [${memoryType}] ${r.memory.substring(0, 80)}... (score: ${r.score.toFixed(3)})`
      );
    });
  } catch (e) {
    console.log('❌ Error searching semantic:', e.response?.data || e.message);
  }

  // Test 4: Search for procedural memory
  console.log('\n4. Searching for procedural memory...');
  try {
    const searchRes = await axios.post(`${BASE}/search`, {
      text: 'openTab navigate',
      config: {
        filters: {
          userId: 'test-real-user',
          conversationId: '1753172354003',
          memoryType: 'procedural',
        },
        limit: 5,
      },
    });
    console.log('Procedural search results:', searchRes.data.results.length);
    searchRes.data.results.forEach((r, i) => {
      const memoryType = r.metadata?.memoryType || 'unknown';
      console.log(
        `  ${i + 1}. [${memoryType}] ${r.memory.substring(0, 80)}... (score: ${r.score.toFixed(3)})`
      );
    });
  } catch (e) {
    console.log('❌ Error searching procedural:', e.response?.data || e.message);
  }

  // Test 5: Search without memoryType filter (should return both)
  console.log('\n5. Searching without memoryType filter...');
  try {
    const searchRes = await axios.post(`${BASE}/search`, {
      text: 'open tab Google',
      config: {
        filters: {
          userId: 'test-real-user',
          conversationId: '1753172354003',
        },
        limit: 10,
      },
    });
    console.log('Mixed search results:', searchRes.data.results.length);
    searchRes.data.results.forEach((r, i) => {
      const memoryType = r.metadata?.memoryType || 'unknown';
      console.log(
        `  ${i + 1}. [${memoryType}] ${r.memory.substring(0, 80)}... (score: ${r.score.toFixed(3)})`
      );
    });
  } catch (e) {
    console.log('❌ Error searching mixed:', e.response?.data || e.message);
  }

  console.log('\n🎉 End-to-end memory test completed!');
}

testEndToEndMemory().catch(console.error);
