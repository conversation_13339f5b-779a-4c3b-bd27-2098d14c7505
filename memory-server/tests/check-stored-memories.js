const http = require('http');

function checkStoredMemories() {
  console.log('🔍 Checking stored memories...');

  // Simulate the request sent by api-cf to memory-server after processing
  const searchData = JSON.stringify({
    text: 'automation twitter',
    config: {
      limit: 3,
      filters: {
        userId: 'xxx', // userId automatically added by api-cf
        conversationId: '1755862244309',
        agentId: 'browser',
      },
    },
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/memory/search',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(searchData),
    },
    timeout: 10000,
  };

  console.log('Sending request to:', `http://${options.hostname}:${options.port}${options.path}`);
  console.log('Request data:', searchData);

  const req = http.request(options, res => {
    console.log('Status:', res.statusCode);
    console.log('Headers:', res.headers);

    let data = '';
    res.on('data', chunk => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('Response data length:', data.length);
      console.log('Raw response data:', data);

      try {
        const response = JSON.parse(data);
        console.log('\n📊 Stored Memory Data:');
        console.log('='.repeat(50));

        if (response.results && response.results.length > 0) {
          console.log(`Total memory count: ${response.results.length}`);
          console.log('\nMemory details:');

          response.results.forEach((result, index) => {
            const memory = result;
            console.log(`\n${index + 1}. Memory ID: ${memory.id}`);
            console.log(`   Type: ${memory.metadata?.memoryType || 'unknown'}`);
            console.log(`   User ID: ${memory.metadata?.userId || 'N/A'}`);
            console.log(`   Agent ID: ${memory.metadata?.agentId || 'N/A'}`);
            console.log(`   Conversation ID: ${memory.metadata?.conversationId || 'N/A'}`);
            console.log(`   Created at: ${memory.metadata?.created_at || 'N/A'}`);
            console.log(`   Updated at: ${memory.metadata?.updated_at || 'N/A'}`);
            console.log(`   Similarity score: ${memory.score}`);
            console.log(`   Data length: ${memory.memory ? memory.memory.length : 0} characters`);

            // Show first 300 characters of data
            if (memory.memory) {
              const preview =
                memory.memory.length > 300
                  ? memory.memory.substring(0, 300) + '...'
                  : memory.memory;
              console.log(`   Memory content: ${preview}`);
            }
          });
        } else {
          console.log('❌ No stored memory data found');
        }
      } catch (error) {
        console.log('Failed to parse response:', error.message);
      }
    });
  });

  req.on('error', error => {
    console.error('Request failed:', error.message);
  });

  req.on('timeout', () => {
    console.error('Request timeout');
    req.destroy();
  });

  req.write(searchData);
  req.end();
}

checkStoredMemories();
