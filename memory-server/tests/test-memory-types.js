const axios = require('axios');
const assert = require('assert');

const BASE = 'http://localhost:3000/memory';

async function run() {
  console.log('🧪 Testing Memory Type Classification and Search Priority...\n');

  // Test 1: Site Memory (should not include userId in filters)
  console.log('1. Testing site memory classification...');
  let res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 1,
        conversation_id: 1753172354000,
        role: 'user',
        content: 'Q: How to post on x.com? A: Click the post button, type content, click submit.',
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-site-memory',
        hostname: 'x.com',
      },
      metadata: {
        memoryType: 'site',
        hostname: 'x.com',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Site memory addition passed');

  // Test 2: Search site memory (should not require userId)
  console.log('\n2. Testing site memory search...');
  res = await axios.post(`${BASE}/search`, {
    text: 'post on x.com',
    config: {
      filters: {
        hostname: 'x.com',
        memoryType: 'site',
      },
      limit: 5,
    },
  });
  assert.strictEqual(res.status, 200);
  assert(Array.isArray(res.data.results), 'results should be an array');
  if (res.data.results.length > 0) {
    console.log('Found site memory:', res.data.results[0].memory);
  }
  console.log('✅ Site memory search passed');

  // Test 3: Semantic Memory
  console.log('\n3. Testing semantic memory classification...');
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 2,
        conversation_id: 1753172354001,
        role: 'user',
        content: 'React is a JavaScript library for building user interfaces',
        status: 'completed',
      },
      {
        id: 3,
        conversation_id: 1753172354001,
        role: 'assistant',
        content: 'Yes, React is developed by Facebook and uses component-based architecture',
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-memory-types-1',
        conversationId: '1753172354001',
      },
      metadata: {
        memoryType: 'semantic',
        topic: 'React',
        concept: 'JavaScript library',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Semantic memory addition passed');

  // Test 4: Search semantic memory
  console.log('\n4. Testing semantic memory search...');
  res = await axios.post(`${BASE}/search`, {
    text: 'React JavaScript library',
    config: {
      filters: {
        userId: 'test-memory-types-1',
        conversationId: '1753172354001',
        memoryType: 'semantic',
      },
      limit: 5,
    },
  });
  assert.strictEqual(res.status, 200);
  assert(Array.isArray(res.data.results), 'results should be an array');
  if (res.data.results.length > 0) {
    console.log('Found semantic memory:', res.data.results[0].memory);
  }
  console.log('✅ Semantic memory search passed');

  // Test 5: Procedural Memory (Workflow Mode)
  console.log('\n5. Testing procedural memory (workflow mode)...');
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 4,
        conversation_id: 1753172354002,
        role: 'tool',
        name: 'WebToolkit_openTab',
        content: JSON.stringify({
          success: true,
          data: {
            tabId: 300,
            url: 'https://www.github.com',
          },
        }),
        status: 'completed',
      },
      {
        id: 5,
        conversation_id: 1753172354002,
        role: 'tool',
        name: 'WebToolkit_navigate',
        content: JSON.stringify({
          success: true,
          data: {
            url: 'https://www.github.com',
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-memory-types-2',
        conversationId: '1753172354002',
        agentId: 'browser-agent-types-1',
      },
      metadata: {
        memoryType: 'procedural',
        workflowId: 'workflow-types-1',
        taskId: 'task-types-1',
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Procedural memory (workflow mode) addition passed');

  // Test 6: Search procedural memory by workflow context
  console.log('\n6. Testing procedural memory search by workflow context...');
  res = await axios.post(`${BASE}/search`, {
    text: 'openTab navigate',
    config: {
      filters: {
        userId: 'test-memory-types-2',
        workflowId: 'workflow-types-1',
        taskId: 'task-types-1',
        memoryType: 'procedural',
      },
      limit: 5,
    },
  });
  assert.strictEqual(res.status, 200);
  assert(Array.isArray(res.data.results), 'results should be an array');
  if (res.data.results.length > 0) {
    console.log('Found workflow procedural memory:', res.data.results[0].memory);
  }
  console.log('✅ Workflow context search passed');

  // Test 7: Procedural Memory (Chat Mode)
  console.log('\n7. Testing procedural memory (chat mode)...');
  res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 6,
        conversation_id: 1753172354003,
        role: 'tool',
        name: 'WebToolkit_click',
        content: JSON.stringify({
          success: true,
          data: 'clicked',
          action: {
            tool: 'click',
            args: {
              selectorOrIndex: 'button.login',
            },
          },
        }),
        status: 'completed',
      },
    ],
    config: {
      filters: {
        userId: 'test-memory-types-3',
        conversationId: '1753172354003',
        agentId: 'browser-agent-types-2',
      },
      metadata: {
        memoryType: 'procedural',
        // No workflowId - chat mode
      },
    },
  });
  assert.strictEqual(res.status, 201);
  console.log('✅ Procedural memory (chat mode) addition passed');

  // Test 8: Search procedural memory by chat context
  console.log('\n8. Testing procedural memory search by chat context...');
  res = await axios.post(`${BASE}/search`, {
    text: 'click button',
    config: {
      filters: {
        userId: 'test-memory-types-3',
        conversationId: '1753172354003',
        agentId: 'browser-agent-types-2',
        memoryType: 'procedural',
      },
      limit: 5,
    },
  });
  assert.strictEqual(res.status, 200);
  assert(Array.isArray(res.data.results), 'results should be an array');
  if (res.data.results.length > 0) {
    console.log('Found chat procedural memory:', res.data.results[0].memory);
  }
  console.log('✅ Chat context search passed');

  // Test 9: Mixed memory type search (should return all types)
  console.log('\n9. Testing mixed memory type search...');
  res = await axios.post(`${BASE}/search`, {
    text: 'React openTab click',
    config: {
      filters: {
        userId: 'test-memory-types-1',
      },
      limit: 10,
    },
  });
  assert.strictEqual(res.status, 200);
  assert(Array.isArray(res.data.results), 'results should be an array');
  console.log(`Found ${res.data.results.length} memories of mixed types`);
  res.data.results.forEach((result, index) => {
    const memoryType = result.metadata?.memoryType || 'unknown';
    console.log(`Memory ${index + 1} (${memoryType}):`, result.memory.substring(0, 80) + '...');
  });
  console.log('✅ Mixed memory type search passed');

  // Test 10: Search priority verification
  console.log('\n10. Testing search priority verification...');

  // Search for site memory with hostname priority
  res = await axios.post(`${BASE}/search`, {
    text: 'post on x.com',
    config: {
      filters: {
        hostname: 'x.com',
        memoryType: 'site',
      },
      limit: 3,
    },
  });
  assert.strictEqual(res.status, 200);
  if (res.data.results.length > 0) {
    const firstResult = res.data.results[0];
    console.log('Site memory search result:', firstResult.memory.substring(0, 60) + '...');
    console.log('Score:', firstResult.score);
  }

  // Search for procedural memory with workflow priority
  res = await axios.post(`${BASE}/search`, {
    text: 'openTab navigate',
    config: {
      filters: {
        userId: 'test-memory-types-2',
        workflowId: 'workflow-types-1',
        memoryType: 'procedural',
      },
      limit: 3,
    },
  });
  assert.strictEqual(res.status, 200);
  if (res.data.results.length > 0) {
    const firstResult = res.data.results[0];
    console.log('Procedural memory search result:', firstResult.memory.substring(0, 60) + '...');
    console.log('Score:', firstResult.score);
  }

  console.log('✅ Search priority verification passed');

  // Test 11: Memory type validation
  console.log('\n11. Testing memory type validation...');

  // Test invalid memory type
  try {
    await axios.post(`${BASE}/add`, {
      messages: [
        {
          id: 7,
          conversation_id: 1753172354004,
          role: 'user',
          content: 'Test with invalid memory type',
          status: 'completed',
        },
      ],
      config: {
        filters: {
          userId: 'test-memory-types-4',
          conversationId: '1753172354004',
        },
        metadata: {
          memoryType: 'invalid_type',
        },
      },
    });
    console.log('⚠️ Invalid memory type was accepted (may need validation)');
  } catch (e) {
    if (e.response?.status === 400) {
      console.log('✅ Invalid memory type properly rejected');
    } else {
      console.log('⚠️ Unexpected error for invalid memory type:', e.message);
    }
  }

  console.log('\n🎉 All memory type classification and search priority tests passed!');
}

run().catch(e => {
  console.error('❌ Test failed:', e.message);
  if (e.response) {
    console.error('Response status:', e.response.status);
    console.error('Response data:', e.response.data);
  }
  process.exit(1);
});
