import { createClient, SupabaseClient } from '@supabase/supabase-js';
import {
  MiniApp,
  UpdateMiniAppRequest,
  ListMiniappResponseType,
  Installation,
} from '@the-agent/shared';
import { MiniAppStore } from '../interfaces/miniapp.store';
import { sha256Hex } from '../utils/crypto';

export class SupabaseMiniAppStore implements MiniAppStore {
  private supabase: SupabaseClient;
  private defaultDeveloping = {
    code: '',
    version: 1,
    updated_at: Date.now(),
  };
  private defaultInstallation = {
    code: '',
    changelogs: '',
    deployed_at: Date.now(),
  };

  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  async saveMiniApp(miniapp: MiniApp, userId: string): Promise<void> {
    const convId = await sha256Hex(`${userId}:${miniapp.conversation_id}`);
    const { error } = await this.supabase.from('miniapps').insert({
      user_id: userId,
      local_id: miniapp.id,
      conversation_id: convId,
      name: miniapp.name,
      developing: miniapp.developing ?? this.defaultDeveloping,
      installation: miniapp.installation ?? this.defaultInstallation,
      history: miniapp.history,
      status: 'active',
    });
    if (error) {
      throw new Error(`Failed to save miniapp: ${error.message}`);
    }
  }

  async updateMiniApp(userId: string, request: UpdateMiniAppRequest): Promise<void> {
    // Fetch current miniapp
    const { data: currentRows, error: fetchError } = await this.supabase
      .from('miniapps')
      .select('name, developing, installation, history, status')
      .eq('user_id', userId)
      .eq('local_id', request.id)
      .limit(1);
    if (fetchError) {
      throw new Error(`Failed to load miniapp: ${fetchError.message}`);
    }
    const current = currentRows && currentRows[0];
    if (!current) return;

    const updateData: Record<string, unknown> = {};
    if (request.name !== undefined) updateData.name = request.name;
    if (request.developing !== undefined) updateData.developing = request.developing;
    if (request.status !== undefined) updateData.status = request.status;

    // Add installation to history if code doesn't already exist in history
    if (request.installation !== undefined) {
      const currentHistory = Array.isArray(current.history) ? current.history : [];
      const codeExistsInHistory = currentHistory.some(
        (historyInstallation: Installation) =>
          historyInstallation.code === request.installation?.code
      );

      if (!codeExistsInHistory) {
        const newHistory = [...currentHistory];
        newHistory.push(request.installation);
        updateData.installation = request.installation ?? this.defaultInstallation;
        updateData.history = newHistory;
      }
    }

    if (Object.keys(updateData).length === 0) return;

    const { error } = await this.supabase
      .from('miniapps')
      .update(updateData)
      .eq('user_id', userId)
      .eq('local_id', request.id);
    if (error) {
      throw new Error(`Failed to update miniapp: ${error.message}`);
    }
  }

  async listMiniApps(userId: string, startFrom: number): Promise<ListMiniappResponseType[]> {
    const { data, error } = await this.supabase
      .from('miniapps')
      .select(
        `
        local_id,
        name,
        developing,
        installation,
        history,
        status,
        conversations!inner (
          local_id,
          type,
          last_selected_at,
          messages (*)
        )
      `
      )
      .eq('user_id', userId)
      .eq('conversations.user_id', userId)
      .eq('conversations.status', 'active')
      .gte('local_id', startFrom);

    if (error) {
      throw new Error(`Failed to list miniapps: ${error.message}`);
    }

    return (data || []).map((row: any) => ({
      id: row.local_id,
      conversation_id: row.conversations.local_id,
      name: row.name,
      developing: this.parseJson(row.developing),
      installation: this.parseJson(row.installation),
      history: Array.isArray(row.history) ? row.history : [],
      status: row.status ?? 'active',
      conversation: {
        id: row.conversations.local_id,
        type: row.conversations.type,
        last_selected_at: row.conversations.last_selected_at || row.conversations.local_id,
        messages: (row.conversations.messages || []).map((message: any) => ({
          id: message.local_id,
          conversation_id: row.conversations.local_id,
          role: message.role,
          content: message.content,
          reasoning: message.reasoning,
          tool_calls: this.parseJson(message.tool_calls),
          tool_call_id: message.tool_call_id,
          name: message.name,
          status: message.status,
          error: message.error,
          actor: message.actor,
          task_id: message.task_id,
          run_id: message.run_id,
          agent_id: message.agent_id,
          metadata: this.parseJson(message.metadata),
        })),
      },
    }));
  }

  private parseJson(json: unknown): any | null {
    if (typeof json === 'string') {
      return JSON.parse(json);
    }
    return json;
  }
}
