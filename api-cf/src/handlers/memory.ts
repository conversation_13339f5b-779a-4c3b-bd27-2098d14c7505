import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { GatewayServiceError } from '../types/service';
import { getUserBalance } from '../d1/user';
import { deductUserCredits } from '../d1/user';
import { EMBEDDING_MODEL } from '../utils/common';
import { addMessagesToMemory, searchMemory, getSitesWithMemory } from '../utils/memory';
import {
  Message,
  AddMemoryRequestSchema,
  SearchMemoryResponseSchema,
  AddMemoryResponseSchema,
  AddMemoryOptions,
  MemoryConfig,
  MemoryType,
  SearchMemoryRequestSchema,
  Entity,
  SearchMemoryRequestSchemaV2,
  GetSitesWithMemoryResponseSchema,
  extractTextFromContent,
} from '@the-agent/shared';

export class SearchMemory extends OpenAPIRoute {
  schema = {
    request: {
      query: SearchMemoryRequestSchema,
    },
    responses: {
      '200': {
        description: 'Search memory successfully',
        content: {
          'application/json': {
            schema: SearchMemoryResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    if (!userId) {
      throw new GatewayServiceError(401, 'Unauthorized');
    }

    const currentCredits = await getUserBalance(c.env, userId);
    if (currentCredits <= 0) {
      throw new GatewayServiceError(402, 'Insufficient credits');
    }

    const query = c.req.query();
    const { text, limit, ...entityParams } = query;
    if (!text) {
      throw new GatewayServiceError(400, 'Invalid request: text is required');
    }
    const filters: Entity = { userId };

    const entityParamKeys: (keyof Entity)[] = [
      'conversationId',
      'agentId',
      'runId',
      'taskId',
      'workflowId',
      'hostname',
      'memoryType',
    ];

    for (const key of entityParamKeys) {
      if (entityParams[key]) {
        if (key === 'memoryType') {
          filters[key] = entityParams[key] as MemoryType;
        } else {
          filters[key] = entityParams[key] as string;
        }
      }
    }
    const result = await searchMemory(c.env, text, {
      limit: parseInt(limit ?? '3'),
      filters,
    });

    // - 50 credit overhead
    // - 100 credit for supabase API cost
    // - embedding generation: 10000 per 1M tokens, 100 token per credit
    const totalCost = 150 + text.length / 100;
    await deductUserCredits(c.env, userId, totalCost, EMBEDDING_MODEL);

    // Return success response with CORS headers
    return c.json(
      {
        results: result.results,
        relations: result.relations,
      },
      200
    );
  }
}

export class AddMemory extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: AddMemoryRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Add memory successfully',
        content: {
          'application/json': {
            schema: AddMemoryResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const body = await c.req.json();

    let request: { messages: Message[]; config?: MemoryConfig };
    try {
      request = AddMemoryRequestSchema.parse(body);
    } catch (error) {
      console.error('Invalid message format:', error);
      throw new GatewayServiceError(400, 'Invalid message format');
    }

    const currentCredits = await getUserBalance(c.env, userId);
    if (currentCredits <= 0) {
      throw new GatewayServiceError(402, 'Insufficient credits');
    }

    if (!request.messages) {
      throw new GatewayServiceError(400, 'Invalid request');
    }

    const messages: Message[] = request.messages.filter(message => {
      const content = extractTextFromContent(message.content);
      if (content.length > 0) {
        message.content = content;
        return message;
      }
    });

    // For site memory, don't add user-specific metadata
    const isSiteMemory = request.config?.metadata?.memoryType === 'site';

    const config: AddMemoryOptions = {
      metadata: {
        ...request.config?.metadata,
        // Only add userId for non-site memory
        ...(isSiteMemory ? {} : { userId }),
      },
      filters: {
        ...request.config?.filters,
        // Only add userId for non-site memory
        ...(isSiteMemory ? {} : { userId }),
      },
    };

    const result = await addMessagesToMemory(c.env, messages, config);

    const totalTextLength = messages.reduce((acc, p) => acc + (p.content?.length ?? 0), 0);
    const totalMessages = messages.length;
    // - 50 credit overhead
    // - 100 credit for supabase API cost
    // - embedding generation: 10000 per 1M tokens, 100 token per credit
    const totalCost = 150 * totalMessages + totalTextLength / 100;
    await deductUserCredits(c.env, userId, totalCost, EMBEDDING_MODEL);

    // Return success response with CORS headers
    return c.json(result, 200);
  }
}

export class SearchMemoryV2 extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: SearchMemoryRequestSchemaV2,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Search memory successfully',
        content: {
          'application/json': {
            schema: SearchMemoryResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    if (!userId) {
      throw new GatewayServiceError(401, 'Unauthorized');
    }

    const currentCredits = await getUserBalance(c.env, userId);
    if (currentCredits <= 0) {
      throw new GatewayServiceError(402, 'Insufficient credits');
    }

    const body = await c.req.json();
    const {
      text,
      config: { limit, filters },
    } = body;
    if (!text) {
      throw new GatewayServiceError(400, 'Invalid request: text is required');
    }

    // Extract text from MessageContent if needed
    const extractedText = extractTextFromContent(text);
    if (!extractedText) {
      throw new GatewayServiceError(400, 'Invalid request: text extraction failed');
    }

    // For site memory searches, don't add userId
    const isSiteMemory = filters.memoryType === 'site';

    const newFilters: Entity = {};
    const entityParamKeys: (keyof Entity)[] = [
      'conversationId',
      'agentId',
      'runId',
      'taskId',
      'workflowId',
      'hostname',
      'memoryType',
    ];

    // Only add userId for non-site memory
    if (!isSiteMemory) {
      newFilters.userId = userId;
    }

    for (const key of entityParamKeys) {
      if (filters[key]) {
        if (key === 'memoryType') {
          newFilters[key] = filters[key] as MemoryType;
        } else {
          newFilters[key] = filters[key] as string;
        }
      }
    }
    const result = await searchMemory(c.env, extractedText, {
      limit,
      filters: newFilters,
    });

    // - 50 credit overhead
    // - 100 credit for supabase API cost
    // - embedding generation: 10000 per 1M tokens, 100 token per credit
    const totalCost = 150 + extractedText.length / 100;
    await deductUserCredits(c.env, userId, totalCost, EMBEDDING_MODEL);

    // Return success response with CORS headers
    return c.json(
      {
        results: result.results,
        relations: result.relations,
      },
      200
    );
  }
}

export class GetSitesWithMemory extends OpenAPIRoute {
  schema = {
    responses: {
      '200': {
        description: 'Get sites with memory successfully',
        content: {
          'application/json': {
            schema: GetSitesWithMemoryResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    if (!userId) {
      throw new GatewayServiceError(401, 'Unauthorized');
    }

    try {
      const sites = await getSitesWithMemory(c.env);
      return c.json({ sites }, 200);
    } catch (error) {
      console.error('Failed to get sites with memory:', error);
      throw new GatewayServiceError(500, 'Failed to get sites with memory');
    }
  }
}
